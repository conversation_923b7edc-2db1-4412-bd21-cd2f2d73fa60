#!/usr/bin/env python
"""
Script to run all API tests in sequence
"""
import sys
import os
import subprocess
import time

# Add the parent directory to sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

def run_test(test_script):
    """Run a test script and return True if successful"""
    print(f"\n\n{'='*80}")
    print(f"Running {test_script}")
    print(f"{'='*80}\n")
    
    result = subprocess.run(['python', test_script], cwd=os.path.dirname(os.path.abspath(__file__)))
    
    if result.returncode == 0:
        print(f"\n✅ {test_script} completed successfully")
        return True
    else:
        print(f"\n❌ {test_script} failed with return code {result.returncode}")
        return False

def main():
    """Run all test scripts in sequence"""
    test_scripts = [
        'test_01_user_registration.py',
        'test_02_deposit_management.py',
        'test_03_loan_requests.py',
        'test_04_lottery.py',
        'test_05_tickets.py',
        'test_06_voting.py',
        'test_07_issues.py'
    ]
    
    success_count = 0
    failure_count = 0
    
    for script in test_scripts:
        if run_test(script):
            success_count += 1
        else:
            failure_count += 1
        
        # Add a small delay between tests
        time.sleep(1)
    
    print(f"\n\n{'='*80}")
    print(f"Test Summary: {success_count} succeeded, {failure_count} failed")
    print(f"{'='*80}\n")
    
    return failure_count == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)#!/usr/bin/env python
"""
Script to run all API tests in sequence
"""
import sys
import os
import subprocess
import time

# Add the parent directory to sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

def run_test(test_script):
    """Run a test script and return True if successful"""
    print(f"\n\n{'='*80}")
    print(f"Running {test_script}")
    print(f"{'='*80}\n")
    
    result = subprocess.run(['python', test_script], cwd=os.path.dirname(os.path.abspath(__file__)))
    
    if result.returncode == 0:
        print(f"\n✅ {test_script} completed successfully")
        return True
    else:
        print(f"\n❌ {test_script} failed with return code {result.returncode}")
        return False

def main():
    """Run all test scripts in sequence"""
    test_scripts = [
        'test_01_user_registration.py',
        'test_02_create_deposit.py',
        'test_03_join_deposit.py',
        'test_04_create_loan_request.py',
        'test_05_approve_loan_request.py'
    ]
    
    success_count = 0
    failure_count = 0
    
    for script in test_scripts:
        if run_test(script):
            success_count += 1
        else:
            failure_count += 1
        
        # Add a small delay between tests
        time.sleep(1)
    
    print(f"\n\n{'='*80}")
    print(f"Test Summary: {success_count} succeeded, {failure_count} failed")
    print(f"{'='*80}\n")
    
    return failure_count == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)