<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>

    <!-- External Dependencies -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --light-bg: #f8f9fa;
            --dark-bg: #2c3e50;
            --sidebar-width: 300px;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: var(--light-bg);
            margin: 0;
            padding: 0;
        }

        /* Main Container */
        .documentation-container {
            display: flex;
            min-height: 100vh;
        }

        /* Sidebar Styles */
        .sidebar {
            width: var(--sidebar-width);
            background: linear-gradient(135deg, var(--dark-bg) 0%, #34495e 100%);
            color: white;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }

        .sidebar-header {
            padding: 20px;
            background: rgba(0,0,0,0.2);
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .sidebar-header h3 {
            margin: 0;
            font-size: 1.2rem;
            font-weight: 600;
        }

        .sidebar-header p {
            margin: 5px 0 0 0;
            font-size: 0.9rem;
            opacity: 0.8;
        }

        /* App Navigation */
        .app-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .app-item {
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .app-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: transparent;
        }

        .app-header:hover {
            background: rgba(255,255,255,0.1);
        }

        .app-header.active {
            background: var(--secondary-color);
        }

        .app-name {
            font-weight: 600;
            font-size: 1rem;
        }

        .app-description {
            font-size: 0.8rem;
            opacity: 0.7;
            margin-top: 2px;
        }

        .toggle-icon {
            transition: transform 0.3s ease;
        }

        .toggle-icon.rotated {
            transform: rotate(90deg);
        }

        /* Endpoints List */
        .endpoints-list {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
            background: rgba(0,0,0,0.2);
        }

        .endpoints-list.expanded {
            max-height: 500px;
        }

        .endpoint-item {
            padding: 10px 20px 10px 40px;
            cursor: pointer;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }

        .endpoint-item:hover {
            background: rgba(255,255,255,0.1);
            border-left-color: var(--success-color);
        }

        .endpoint-item.active {
            background: var(--success-color);
            border-left-color: white;
        }

        /* HTTP Method Badges */
        .endpoint-method {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 0.7rem;
            font-weight: bold;
            margin-right: 8px;
        }

        .method-get { background: var(--success-color); }
        .method-post { background: var(--secondary-color); }
        .method-put { background: var(--warning-color); }
        .method-delete { background: var(--danger-color); }
        .method-patch { background: #9b59b6; }
        .method-multiple { background: #6c757d; }

        /* Main Content Area */
        .main-content {
            margin-left: var(--sidebar-width);
            flex: 1;
            padding: 30px;
            background: white;
        }

        /* Content Header */
        .content-header {
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid var(--light-bg);
        }

        .header-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .content-header h1 {
            color: var(--dark-bg);
            margin-bottom: 10px;
            font-weight: 700;
        }

        .content-header p {
            color: #666;
            font-size: 1.1rem;
            margin: 0;
        }

        /* Action Buttons */
        .action-buttons {
            display: flex;
            gap: 10px;
        }

        .btn-swagger {
            background: #ff6b35;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .btn-swagger:hover {
            background: #e55a2b;
            color: white;
            text-decoration: none;
        }

        .btn-redoc {
            background: #39b982;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .btn-redoc:hover {
            background: #2d8f66;
            color: white;
            text-decoration: none;
        }

        /* Endpoint Documentation Styles */
        .endpoint-section {
            margin-bottom: 40px;
            padding: 25px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border: 1px solid #e9ecef;
        }

        .endpoint-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e9ecef;
            flex-wrap: wrap;
            gap: 15px;
        }

        .endpoint-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--dark-bg);
            margin: 0;
        }

        .endpoint-url {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            background: var(--light-bg);
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 0.9rem;
            color: var(--dark-bg);
            border: 1px solid #dee2e6;
        }

        .endpoint-description {
            color: #666;
            margin-bottom: 25px;
            line-height: 1.6;
            font-size: 1rem;
        }

        /* Parameters Section */
        .parameters-section {
            margin-bottom: 25px;
        }

        .section-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--dark-bg);
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .section-title i {
            margin-right: 8px;
            color: var(--secondary-color);
        }

        .parameters-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }

        .parameters-table th,
        .parameters-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }

        .parameters-table th {
            background: var(--light-bg);
            font-weight: 600;
            color: var(--dark-bg);
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .parameters-table tbody tr:hover {
            background: #f8f9fa;
        }

        .param-name {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-weight: 600;
            color: var(--secondary-color);
            background: #f8f9fa;
            padding: 4px 8px;
            border-radius: 4px;
        }

        .param-type {
            background: #e3f2fd;
            color: #1976d2;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 500;
            text-transform: uppercase;
        }

        .param-required {
            background: #ffebee;
            color: #c62828;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 500;
            text-transform: uppercase;
        }
        /* Request Examples Section */
        .request-section {
            margin-top: 25px;
            margin-bottom: 25px;
        }

        .request-tabs {
            display: flex;
            margin-bottom: 15px;
            border-bottom: 1px solid #e9ecef;
            gap: 5px;
        }

        .request-tab {
            padding: 10px 20px;
            cursor: pointer;
            border: none;
            background: none;
            color: #666;
            font-weight: 500;
            transition: all 0.3s ease;
            border-radius: 6px 6px 0 0;
            position: relative;
        }

        .request-tab:hover {
            background: #e8f5e8;
            color: var(--success-color);
        }

        .request-tab.active {
            color: var(--success-color);
            background: #e8f5e8;
            border-bottom: 2px solid var(--success-color);
        }

        /* Response Examples Section */
        .response-section {
            margin-top: 25px;
        }

        .response-tabs {
            display: flex;
            margin-bottom: 15px;
            border-bottom: 1px solid #e9ecef;
            gap: 5px;
        }

        .response-tab {
            padding: 10px 20px;
            cursor: pointer;
            border: none;
            background: none;
            color: #666;
            font-weight: 500;
            transition: all 0.3s ease;
            border-radius: 6px 6px 0 0;
            position: relative;
        }

        .response-tab:hover {
            background: #f8f9fa;
            color: var(--secondary-color);
        }

        .response-tab.active {
            color: var(--secondary-color);
            background: #f8f9fa;
            border-bottom: 2px solid var(--secondary-color);
        }

        .json-viewer {
            background: #1e1e1e;
            border-radius: 8px;
            padding: 20px;
            margin-top: 15px;
            overflow-x: auto;
            border: 1px solid #e9ecef;
        }

        .json-viewer pre {
            margin: 0;
            color: #d4d4d4;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.9rem;
            line-height: 1.5;
        }

        /* Schema Viewer Styles */
        .schema-viewer {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-top: 15px;
            border: 1px solid #e9ecef;
        }

        .schema-description {
            margin-bottom: 15px;
            color: #495057;
            font-size: 1rem;
        }

        /* Mobile Responsive Design */
        .mobile-toggle {
            display: none;
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1001;
            background: var(--secondary-color);
            color: white;
            border: none;
            padding: 10px;
            border-radius: 6px;
            cursor: pointer;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        }

        @media (max-width: 768px) {
            .mobile-toggle {
                display: block;
            }

            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.mobile-open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
                padding: 20px 15px;
            }

            .endpoint-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }

            .action-buttons {
                flex-direction: column;
                width: 100%;
            }

            .btn-swagger,
            .btn-redoc {
                justify-content: center;
                width: 100%;
            }

            .parameters-table {
                font-size: 0.8rem;
            }

            .parameters-table th,
            .parameters-table td {
                padding: 8px 10px;
            }

            .response-tabs {
                flex-wrap: wrap;
            }

            .response-tab {
                padding: 8px 15px;
                font-size: 0.9rem;
            }
        }

        @media (max-width: 480px) {
            .main-content {
                padding: 15px 10px;
            }

            .endpoint-section {
                padding: 15px;
            }

            .endpoint-title {
                font-size: 1.2rem;
            }

            .parameters-table {
                display: block;
                overflow-x: auto;
                white-space: nowrap;
            }
        }

        /* Scrollbar Styling */
        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255,255,255,0.1);
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255,255,255,0.3);
            border-radius: 3px;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(255,255,255,0.5);
        }

        /* Smooth Animations */
        * {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Focus States for Accessibility */
        .endpoint-item:focus,
        .app-header:focus,
        .response-tab:focus {
            outline: 2px solid var(--secondary-color);
            outline-offset: 2px;
        }
    </style>
</head>
<body>
    <!-- Mobile Toggle Button -->
    <button class="mobile-toggle" onclick="toggleSidebar()">
        <i class="fas fa-bars"></i>
    </button>

    <div class="documentation-container">
        <!-- Sidebar Navigation -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <h3><i class="fas fa-book"></i> API Documentation</h3>
                <p>{{ description }}</p>
            </div>

            <ul class="app-list">
                {% for app_key, app_data in api_structure.items %}
                <li class="app-item">
                    <div class="app-header" onclick="toggleEndpoints('{{ app_key }}-endpoints')">
                        <div>
                            <div class="app-name">{{ app_data.name }}</div>
                            <div class="app-description">{{ app_data.description }}</div>
                        </div>
                        <i class="fas fa-chevron-right toggle-icon" id="{{ app_key }}-icon"></i>
                    </div>

                    <ul class="endpoints-list" id="{{ app_key }}-endpoints">
                        {% for endpoint in app_data.endpoints %}
                        <li class="endpoint-item" onclick="scrollToEndpoint('{{ app_key }}-{{ forloop.counter0 }}')">
                            <span class="endpoint-method method-{{ endpoint.method|lower }}">{{ endpoint.method }}</span>
                            {{ endpoint.name }}
                        </li>
                        {% endfor %}
                    </ul>
                </li>
                {% endfor %}

                <!-- Deposit Media API Section -->
                <li class="app-item">
                    <div class="app-header" onclick="toggleEndpoints('deposit-media-endpoints')">
                        <div>
                            <div class="app-name">مدیریت رسانه‌های صندوق</div>
                            <div class="app-description">API های مربوط به رسانه‌ها و شمارنده بازدید</div>
                        </div>
                        <i class="fas fa-chevron-right toggle-icon" id="deposit-media-icon"></i>
                    </div>

                    <ul class="endpoints-list" id="deposit-media-endpoints">
                        <li class="endpoint-item" onclick="scrollToEndpoint('deposit-media-api')">
                            <span class="endpoint-method method-multiple">MULTIPLE</span>
                            مدیریت رسانه‌های صندوق
                        </li>
                    </ul>
                </li>

                <!-- Custom Ticket Messages Section -->
                <li class="app-item">
                    <div class="app-header" onclick="toggleEndpoints('ticket-messages-endpoints')">
                        <div>
                            <div class="app-name">پیام‌های تیکت (ناظر)</div>
                            <div class="app-description">API مشاهده پیام‌های تیکت برای ناظران</div>
                        </div>
                        <i class="fas fa-chevron-right toggle-icon" id="ticket-messages-icon"></i>
                    </div>

                    <ul class="endpoints-list" id="ticket-messages-endpoints">
                        <li class="endpoint-item" onclick="scrollToEndpoint('ticket-messages-list')">
                            <span class="endpoint-method method-get">GET</span>
                            مشاهده پیام‌های تیکت
                        </li>
                    </ul>
                </li>

                <!-- Notification System Section -->
                <li class="app-item">
                    <div class="app-header" onclick="toggleEndpoints('notification-endpoints')">
                        <div>
                            <div class="app-name">سیستم نوتیفیکیشن</div>
                            <div class="app-description">نوتیفیکیشن‌های آگاه از ریجن</div>
                        </div>
                        <i class="fas fa-chevron-right toggle-icon" id="notification-icon"></i>
                    </div>

                    <ul class="endpoints-list" id="notification-endpoints">
                        <li class="endpoint-item" onclick="scrollToEndpoint('notification-system')">
                            <span class="endpoint-method method-multiple">SYSTEM</span>
                            سیستم نوتیفیکیشن
                        </li>
                    </ul>
                </li>

                <!-- Guides API Section -->
                <li class="app-item">
                    <div class="app-header" onclick="toggleEndpoints('guides-endpoints')">
                        <div>
                            <div class="app-name">راهنماها</div>
                            <div class="app-description">API دریافت راهنماهای سیستم</div>
                        </div>
                        <i class="fas fa-chevron-right toggle-icon" id="guides-icon"></i>
                    </div>

                    <ul class="endpoints-list" id="guides-endpoints">
                        <li class="endpoint-item" onclick="scrollToEndpoint('guides-api')">
                            <span class="endpoint-method method-get">GET</span>
                            دریافت لیست راهنماها
                        </li>
                    </ul>
                </li>

            </ul>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <div class="content-header">
                <div class="header-top">
                    <div>
                        <h1>{{ title }}</h1>
                        <p>{{ description }}</p>
                    </div>
                    <div class="action-buttons">
                        <a href="{% url 'schema-swagger-ui' %}" class="btn-swagger">
                            <i class="fas fa-code"></i>
                            Swagger UI
                        </a>
                        <a href="{% url 'schema-redoc' %}" class="btn-redoc">
                            <i class="fas fa-book"></i>
                            ReDoc
                        </a>
                    </div>
                </div>
            </div>

            <!-- API Endpoints Documentation -->
            {% for app_key, app_data in api_structure.items %}
                {% for endpoint in app_data.endpoints %}
                <div class="endpoint-section" id="{{ app_key }}-{{ forloop.counter0 }}">
                    <div class="endpoint-header">
                        <h2 class="endpoint-title">{{ endpoint.name }}</h2>
                        <span class="endpoint-method method-{{ endpoint.method|lower }}">{{ endpoint.method }}</span>
                        <code class="endpoint-url">{{ endpoint.url }}</code>
                    </div>

                    <p class="endpoint-description">{{ endpoint.description }}</p>



                    {% if endpoint.parameters %}
                    <div class="parameters-section">
                        <h3 class="section-title">
                            <i class="fas fa-cogs"></i>
                            Parameters
                        </h3>
                        <table class="parameters-table">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Type</th>
                                    <th>Required</th>
                                    <th>Description</th>
                                    <th>Example</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for param in endpoint.parameters %}
                                <tr>
                                    <td><code class="param-name">{{ param.name }}</code></td>
                                    <td>
                                        <span class="param-type">{{ param.type }}</span>
                                        {% if param.data_type %}
                                            <br><small style="color: #666;">({{ param.data_type }})</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if param.required %}
                                            <span class="param-required">Required</span>
                                        {% else %}
                                            <span class="param-type">Optional</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {{ param.description }}
                                    </td>
                                    <td>
                                        {% if param.example %}
                                            <code style="background: #f8f9fa; padding: 2px 6px; border-radius: 3px; font-size: 0.85em;">{{ param.example }}</code>
                                        {% else %}
                                            <span style="color: #999; font-style: italic;">-</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% endif %}

                    <!-- Request Body Examples Section -->
                    {% if endpoint.method == 'POST' or endpoint.method == 'PUT' or endpoint.method == 'PATCH' %}
                    <div class="request-section">
                        <h3 class="section-title">
                            <i class="fas fa-upload"></i>
                            Request Body Examples
                        </h3>


                        <!-- Auto-generated request body based on parameters -->
                        <div class="json-viewer">
                            <pre><code class="language-json">{
{% for param in endpoint.parameters %}{% if param.type == 'body' %}  "{{ param.name }}": {% if param.example %}"{{ param.example }}"{% elif param.data_type == 'integer' %}123{% elif param.data_type == 'boolean' %}true{% elif param.data_type == 'number' %}100.0{% else %}"example_value"{% endif %}{% if not forloop.last %},{% endif %}
{% endif %}{% endfor %}}</code></pre>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Response Schema Section -->
                    {% if endpoint.response_schemas %}
                    <div class="response-section">
                        <h3 class="section-title">
                            <i class="fas fa-database"></i>
                            Response Schema
                        </h3>

                        <div class="response-tabs">
                            {% for schema_type, schema_data in endpoint.response_schemas.items %}
                            <button class="response-tab {% if forloop.first %}active{% endif %}"
                                    onclick="showResponseSchema('{{ app_key }}-{{ forloop.parentloop.counter0 }}', '{{ schema_type }}')">
                                {% if schema_type == 'success' %}
                                    ✅ Success ({{ schema_data.status_code }})
                                {% elif schema_type|slice:":6" == 'error_' %}
                                    ❌ Error ({{ schema_data.status_code }})
                                {% else %}
                                    {{ schema_type|title }} ({{ schema_data.status_code }})
                                {% endif %}
                            </button>
                            {% endfor %}
                        </div>

                        {% for schema_type, schema_data in endpoint.response_schemas.items %}
                        <div class="schema-viewer" id="{{ app_key }}-{{ forloop.parentloop.counter0 }}-schema-{{ schema_type }}"
                             style="{% if not forloop.first %}display: none;{% endif %}">
                            <div class="schema-description">
                                <strong>{{ schema_data.description }}</strong>
                            </div>

                            {% if schema_data.properties %}
                            <table class="parameters-table" style="margin-top: 15px;">
                                <thead>
                                    <tr>
                                        <th>Field</th>
                                        <th>Type</th>
                                        <th>Required</th>
                                        <th>Description</th>
                                        <th>Example</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for property in schema_data.properties %}
                                    <tr>
                                        <td><code class="param-name">{{ property.name }}</code></td>
                                        <td>
                                            <span class="param-type">{{ property.type }}</span>
                                            {% if property.items_type %}
                                                <br><small style="color: #666;">Array of {{ property.items_type }}</small>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if property.required %}
                                                <span class="param-required">Required</span>
                                            {% else %}
                                                <span class="param-type">Optional</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ property.description }}</td>
                                        <td>
                                            {% if property.example %}
                                                <code style="background: #f8f9fa; padding: 2px 6px; border-radius: 3px; font-size: 0.85em;">{{ property.example }}</code>
                                            {% else %}
                                                <span style="color: #999; font-style: italic;">-</span>
                                            {% endif %}
                                        </td>
                                    </tr>

                                    <!-- Nested properties for object types -->
                                    {% if property.nested_properties %}
                                        {% for nested_prop in property.nested_properties %}
                                        <tr style="background: #f8f9fa;">
                                            <td style="padding-left: 30px;">
                                                <code class="param-name">{{ property.name }}.{{ nested_prop.name }}</code>
                                            </td>
                                            <td>
                                                <span class="param-type">{{ nested_prop.type }}</span>
                                            </td>
                                            <td>
                                                {% if nested_prop.required %}
                                                    <span class="param-required">Required</span>
                                                {% else %}
                                                    <span class="param-type">Optional</span>
                                                {% endif %}
                                            </td>
                                            <td>{{ nested_prop.description }}</td>
                                            <td>
                                                {% if nested_prop.example %}
                                                    <code style="background: #fff; padding: 2px 6px; border-radius: 3px; font-size: 0.85em;">{{ nested_prop.example }}</code>
                                                {% else %}
                                                    <span style="color: #999; font-style: italic;">-</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    {% endif %}
                                    {% endfor %}
                                </tbody>
                            </table>
                            {% endif %}
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}

                    <div class="response-section">
                        <h3 class="section-title">
                            <i class="fas fa-code"></i>
                            Response Examples
                        </h3>

                        <div class="response-tabs">
                            {% for response_type, response_data in endpoint.response_examples.items %}
                            <button class="response-tab {% if forloop.first %}active{% endif %}"
                                    onclick="showResponse('{{ app_key }}-{{ forloop.parentloop.counter0 }}', '{{ response_type }}')">
                                {% if response_type == 'success' %}
                                    ✅ Success (200/201)
                                {% elif response_type == 'error_400' %}
                                    ❌ Bad Request (400)
                                {% elif response_type == 'error_401' %}
                                    ❌ Unauthorized (401)
                                {% elif response_type == 'error_403' %}
                                    ❌ Forbidden (403)
                                {% elif response_type == 'error_404' %}
                                    ❌ Not Found (404)
                                {% elif response_type == 'error_422' %}
                                    ❌ Validation Error (422)
                                {% elif response_type == 'error_500' %}
                                    ❌ Server Error (500)
                                {% elif response_type == 'validation_error' %}
                                    ❌ Validation Error (400)
                                {% elif response_type == 'password_error' %}
                                    ❌ Password Error (400)
                                {% elif response_type == 'password_length_error' %}
                                    ❌ Password Length Error (400)
                                {% elif response_type == 'invalid_code' %}
                                    ❌ Invalid Code (400)
                                {% elif response_type == 'expired_code' %}
                                    ❌ Expired Code (400)
                                {% elif response_type == 'not_found' %}
                                    ❌ Not Found (404)
                                {% elif response_type == 'not_found_error' %}
                                    ❌ Not Found (404)
                                {% elif response_type == 'permission_error' %}
                                    ❌ Permission Denied (403)
                                {% elif response_type == 'already_cancelled_error' %}
                                    ❌ Already Cancelled (400)
                                {% elif response_type == 'error' %}
                                    ❌ Error (400)
                                {% elif response_type|slice:":6" == 'error_' %}
                                    ❌ Error ({{ response_type|slice:"6:" }})
                                {% else %}
                                    {{ response_type|title }} Response
                                {% endif %}
                            </button>
                            {% endfor %}
                        </div>

                        {% for response_type, response_data in endpoint.response_examples.items %}
                        <div class="json-viewer" id="{{ app_key }}-{{ forloop.parentloop.counter0 }}-response-{{ response_type }}"
                             style="{% if not forloop.first %}display: none;{% endif %}">
                            <pre><code class="language-json">{{ response_data|safe }}</code></pre>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endfor %}
            {% endfor %}

            <!-- Deposit Media API Documentation -->
            <div class="endpoint-section" id="deposit-media-api">
                <div class="endpoint-header">
                    <h2 class="endpoint-title">مدیریت رسانه‌های صندوق</h2>
                    <span class="endpoint-method method-multiple">MULTIPLE</span>
                    <code class="endpoint-url">/api/deposits/media/</code>
                </div>

                <p class="endpoint-description">
                    API های مربوط به مدیریت رسانه‌های صندوق شامل ایجاد، ویرایش، حذف و افزایش تعداد بازدید رسانه‌ها.
                    هر رسانه می‌تواند شامل چندین تصویر با اولویت‌های مختلف باشد و دارای شمارنده بازدید است.
                </p>

                <!-- Media View Count Increment API -->
                <div class="parameters-section">
                    <h3 class="section-title">
                        <i class="fas fa-eye"></i>
                        افزایش تعداد بازدید رسانه
                    </h3>

                    <div class="endpoint-header" style="margin-bottom: 15px;">
                        <span class="endpoint-method method-post">POST</span>
                        <code class="endpoint-url">/api/deposits/media/increment-view/</code>
                    </div>

                    <p><strong>توضیحات:</strong> این API برای افزایش تعداد بازدید یک رسانه خاص استفاده می‌شود. از F() expression برای جلوگیری از race condition استفاده می‌کند.</p>

                    <!-- Request Section -->
                    <h4 style="color: var(--success-color); margin-top: 20px;">📤 درخواست:</h4>
                    <div class="json-viewer">
                        <pre><code class="language-json">{
    "media_id": 1
}</code></pre>
                    </div>

                    <!-- Request Parameters Table -->
                    <h4 style="color: var(--secondary-color); margin-top: 20px;">📋 پارامترهای درخواست:</h4>
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>فیلد</th>
                                <th>نوع</th>
                                <th>الزامی</th>
                                <th>توضیحات</th>
                                <th>مثال</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>media_id</code></td>
                                <td>integer</td>
                                <td>✅</td>
                                <td>شناسه رسانه برای افزایش بازدید</td>
                                <td>1</td>
                            </tr>
                        </tbody>
                    </table>

                    <!-- Success Response -->
                    <h4 style="color: var(--success-color); margin-top: 20px;">✅ پاسخ موفق (200):</h4>
                    <div class="json-viewer">
                        <pre><code class="language-json">{
    "status": "success",
    "message": "View count incremented successfully",
    "media_id": 1,
    "current_view_count": 26
}</code></pre>
                    </div>

                    <!-- Error Responses -->
                    <h4 style="color: var(--danger-color); margin-top: 20px;">❌ پاسخ‌های خطا:</h4>

                    <h5 style="color: var(--warning-color); margin-top: 15px;">400 - Bad Request:</h5>
                    <div class="json-viewer">
                        <pre><code class="language-json">{
    "status": "error",
    "message": "media_id is required"
}</code></pre>
                    </div>

                    <h5 style="color: var(--warning-color); margin-top: 15px;">404 - Not Found:</h5>
                    <div class="json-viewer">
                        <pre><code class="language-json">{
    "status": "error",
    "message": "Media not found"
}</code></pre>
                    </div>

                    <h5 style="color: var(--warning-color); margin-top: 15px;">500 - Internal Server Error:</h5>
                    <div class="json-viewer">
                        <pre><code class="language-json">{
    "status": "error",
    "message": "An error occurred: ..."
}</code></pre>
                    </div>

                    <!-- Usage Example -->
                    <h4 style="color: var(--secondary-color); margin-top: 20px;">💡 نحوه استفاده:</h4>
                    <div class="json-viewer">
                        <pre><code class="language-javascript">// JavaScript Example
async function incrementMediaView(mediaId) {
    try {
        const response = await fetch('/api/deposits/media/increment-view/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer YOUR_TOKEN'
            },
            body: JSON.stringify({
                media_id: mediaId
            })
        });

        const data = await response.json();

        if (data.status === 'success') {
            console.log(`View count updated: ${data.current_view_count}`);
            // Update UI with new view count
            updateViewCountDisplay(mediaId, data.current_view_count);
        } else {
            console.error('Error:', data.message);
        }
    } catch (error) {
        console.error('Network error:', error);
    }
}</code></pre>
                    </div>

                    <!-- Important Notes -->
                    <div style="background: #fff3cd; padding: 20px; border-radius: 8px; border-left: 4px solid var(--warning-color); margin-top: 20px;">
                        <h4 style="color: var(--warning-color); margin-bottom: 15px;">⚠️ نکات مهم:</h4>
                        <ul style="margin: 0; padding-left: 20px;">
                            <li><strong>Race Condition:</strong> از F() expression برای جلوگیری از مشکلات همزمانی استفاده می‌شود</li>
                            <li><strong>احراز هویت:</strong> این API نیاز به توکن احراز هویت دارد</li>
                            <li><strong>Atomic Operation:</strong> عملیات افزایش بازدید به صورت atomic انجام می‌شود</li>
                            <li><strong>Performance:</strong> استفاده از update() برای بهبود کارایی</li>
                        </ul>
                    </div>
                </div>

                <!-- Media List with View Count -->
                <div class="parameters-section">
                    <h3 class="section-title">
                        <i class="fas fa-list"></i>
                        تغییرات در لیست رسانه‌ها
                    </h3>

                    <p><strong>بروزرسانی مهم:</strong> فیلد <code>view_count</code> به serializer لیست رسانه‌ها اضافه شده است.</p>

                    <!-- Updated Response Structure -->
                    <h4 style="color: var(--success-color); margin-top: 20px;">✅ ساختار جدید پاسخ لیست رسانه‌ها:</h4>
                    <div class="json-viewer">
                        <pre><code class="language-json">[
    {
        "id": 1,
        "subject": "تصاویر جلسه اول",
        "description": "تصاویر مربوط به جلسه اول صندوق",
        "view_count": 26,  // فیلد جدید
        "created_at": "2023-01-01T10:00:00Z",
        "updated_at": "2023-01-01T12:00:00Z",
        "images": [
            {
                "id": 1,
                "image": "https://example.com/media/deposit_media/image1.jpg",
                "priority": 1
            },
            {
                "id": 2,
                "image": "https://example.com/media/deposit_media/image2.jpg",
                "priority": 2
            }
        ]
    }
]</code></pre>
                    </div>

                    <!-- Field Description -->
                    <h4 style="color: var(--secondary-color); margin-top: 20px;">📋 توضیحات فیلد جدید:</h4>
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>فیلد</th>
                                <th>نوع</th>
                                <th>توضیحات</th>
                                <th>مقدار پیش‌فرض</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>view_count</code></td>
                                <td>integer</td>
                                <td>تعداد دفعات مشاهده این رسانه</td>
                                <td>0</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Custom Issues & Reports Documentation -->
            <div class="endpoint-section" id="issues-reports">
                <div class="endpoint-header">
                    <h2 class="endpoint-title">Issues & Reports System</h2>
                    <span class="endpoint-method method-get">MULTIPLE</span>
                    <code class="endpoint-url">/api/issues/ & /api/ticket/</code>
                </div>

                <p class="endpoint-description">
                    سیستم گزارش‌گیری و تیکت‌ها که امکان ارسال گزارش مشکلات و پیگیری آن‌ها را فراهم می‌کند.
                    این سیستم از مدل یکپارچه Ticket استفاده می‌کند که هم تیکت‌های عادی و هم گزارش‌ها را پشتیبانی می‌کند.
                </p>

                <!-- Report Subjects Section -->
                <div class="parameters-section">
                    <h3 class="section-title">
                        <i class="fas fa-list"></i>
                        1. دریافت لیست موضوعات گزارش
                    </h3>

                    <div class="endpoint-header" style="margin-bottom: 15px;">
                        <span class="endpoint-method method-get">GET</span>
                        <code class="endpoint-url">/api/issues/subjects/</code>
                    </div>

                    <p><strong>توضیحات:</strong> دریافت لیست تمام موضوعات فعال گزارش که کاربران می‌توانند هنگام ایجاد گزارش انتخاب کنند.</p>

                    <h4 style="color: var(--success-color); margin-top: 20px;">✅ نمونه پاسخ موفق:</h4>
                    <div class="json-viewer">
                        <pre><code class="language-json">[
    {
        "id": 1,
        "title": "مشکل فنی",
        "description": "مشکلات مربوط به عملکرد سیستم"
    },
    {
        "id": 2,
        "title": "پیشنهاد",
        "description": "پیشنهادات برای بهبود سیستم"
    },
    {
        "id": 3,
        "title": "شکایت",
        "description": "شکایات و نارضایتی‌ها"
    }
]</code></pre>
                    </div>
                </div>

                <!-- Create Report Section -->
                <div class="parameters-section">
                    <h3 class="section-title">
                        <i class="fas fa-plus"></i>
                        2. ایجاد گزارش جدید
                    </h3>

                    <div class="endpoint-header" style="margin-bottom: 15px;">
                        <span class="endpoint-method method-post">POST</span>
                        <code class="endpoint-url">/api/ticket/create/</code>
                    </div>

                    <p><strong>توضیحات:</strong> ایجاد گزارش جدید با استفاده از سیستم تیکت. توضیحات ارائه شده به عنوان اولین پیام در گزارش ثبت می‌شود.</p>

                    <h4 style="color: var(--secondary-color); margin-top: 20px;">📤 نمونه درخواست:</h4>
                    <div class="json-viewer">
                        <pre><code class="language-json">{
    "ticket_type": "report",
    "subject_id": 1,
    "subject": "مشکل در ورود به سیستم",
    "description": "من نمی‌توانم وارد حساب کاربری خود شوم و پیام خطا دریافت می‌کنم"
}</code></pre>
                    </div>

                    <table class="parameters-table">
                        <thead>
                            <tr>
                                <th>فیلد</th>
                                <th>نوع</th>
                                <th>الزامی</th>
                                <th>توضیحات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>ticket_type</code></td>
                                <td>string</td>
                                <td>✅</td>
                                <td>نوع تیکت - باید "report" باشد</td>
                            </tr>
                            <tr>
                                <td><code>subject_id</code></td>
                                <td>integer</td>
                                <td>✅</td>
                                <td>شناسه موضوع گزارش (از API موضوعات)</td>
                            </tr>
                            <tr>
                                <td><code>subject</code></td>
                                <td>string</td>
                                <td>✅</td>
                                <td>عنوان گزارش</td>
                            </tr>
                            <tr>
                                <td><code>description</code></td>
                                <td>string</td>
                                <td>✅</td>
                                <td>توضیحات گزارش (می‌شود اولین پیام)</td>
                            </tr>
                        </tbody>
                    </table>

                    <h4 style="color: var(--success-color); margin-top: 20px;">✅ نمونه پاسخ موفق:</h4>
                    <div class="json-viewer">
                        <pre><code class="language-json">{
    "id": 1,
    "deposit": null,
    "deposit_name": null,
    "user": {
        "id": 10,
        "fullname": "علی احمدی"
    },
    "ticket_type": "report",
    "subject": "مشکل در ورود به سیستم",
    "description": "من نمی‌توانم وارد حساب کاربری خود شوم",
    "is_closed": false,
    "closed_at": null,
    "created_at": "2023-01-01T10:00:00Z",
    "is_read": false,
    "last_message_date": "2023-01-01T10:00:00Z",
    "report_subject_info": {
        "id": 1,
        "title": "مشکل فنی",
        "description": "مشکلات مربوط به عملکرد سیستم"
    }
}</code></pre>
                    </div>
                </div>

                <!-- List Messages Section -->
                <div class="parameters-section">
                    <h3 class="section-title">
                        <i class="fas fa-comments"></i>
                        3. مشاهده پیام‌های گزارش
                    </h3>

                    <div class="endpoint-header" style="margin-bottom: 15px;">
                        <span class="endpoint-method method-get">GET</span>
                        <code class="endpoint-url">/api/ticket/{ticket_id}/messages/</code>
                    </div>

                    <p><strong>توضیحات:</strong> دریافت لیست تمام پیام‌های یک گزارش. پیام‌های کاربر با نام واقعی و پیام‌های پشتیبانی با نام "پشتیبانی فنی" نمایش داده می‌شوند.</p>

                    <!-- Response Parameters Table -->
                    <h4 style="color: var(--secondary-color); margin-top: 20px;">📋 پارامترهای پاسخ:</h4>
                    <table class="parameters-table">
                        <thead>
                            <tr>
                                <th>فیلد</th>
                                <th>نوع</th>
                                <th>الزامی</th>
                                <th>توضیحات</th>
                                <th>مثال</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>id</code></td>
                                <td>integer</td>
                                <td>✅</td>
                                <td>شناسه پیام</td>
                                <td>1</td>
                            </tr>
                            <tr>
                                <td><code>ticket</code></td>
                                <td>integer</td>
                                <td>✅</td>
                                <td>شناسه تیکت</td>
                                <td>5</td>
                            </tr>
                            <tr>
                                <td><code>user</code></td>
                                <td>object</td>
                                <td>✅</td>
                                <td>اطلاعات کاربر فرستنده</td>
                                <td>-</td>
                            </tr>
                            <tr style="background: #f8f9fa;">
                                <td style="padding-left: 30px;"><code>user.id</code></td>
                                <td>integer</td>
                                <td>✅</td>
                                <td>شناسه کاربر</td>
                                <td>10</td>
                            </tr>
                            <tr style="background: #f8f9fa;">
                                <td style="padding-left: 30px;"><code>user.fullname</code></td>
                                <td>string</td>
                                <td>✅</td>
                                <td>نام کامل کاربر</td>
                                <td>علی احمدی</td>
                            </tr>
                            <tr style="background: #f8f9fa;">
                                <td style="padding-left: 30px;"><code>user.role</code></td>
                                <td>string</td>
                                <td>❌</td>
                                <td>نقش کاربر در صندوق</td>
                                <td>Member</td>
                            </tr>
                            <tr>
                                <td><code>content</code></td>
                                <td>string</td>
                                <td>✅</td>
                                <td>متن پیام</td>
                                <td>سلام، مشکلی دارم</td>
                            </tr>
                            <tr>
                                <td><code>message_type</code></td>
                                <td>string</td>
                                <td>✅</td>
                                <td>نوع پیام: text یا image</td>
                                <td>text</td>
                            </tr>
                            <tr>
                                <td><code>content_image</code></td>
                                <td>string</td>
                                <td>❌</td>
                                <td>URL تصویر (برای پیام تصویری)</td>
                                <td>null</td>
                            </tr>
                            <tr>
                                <td><code>is_read</code></td>
                                <td>boolean</td>
                                <td>✅</td>
                                <td>وضعیت خوانده شدن</td>
                                <td>true</td>
                            </tr>
                            <tr>
                                <td><code>created_at</code></td>
                                <td>string</td>
                                <td>✅</td>
                                <td>تاریخ ایجاد (ISO format)</td>
                                <td>2023-01-01T10:00:00Z</td>
                            </tr>
                            <tr>
                                <td><code>is_self</code></td>
                                <td>boolean</td>
                                <td>✅</td>
                                <td>آیا پیام از طرف کاربر جاری است</td>
                                <td>true</td>
                            </tr>
                            <tr>
                                <td><code>deposit_name</code></td>
                                <td>string</td>
                                <td>❌</td>
                                <td>نام صندوق</td>
                                <td>صندوق خانوادگی</td>
                            </tr>
                            <tr>
                                <td><code>sender_type</code></td>
                                <td>string</td>
                                <td>✅</td>
                                <td>نوع فرستنده: user, manager, support</td>
                                <td>user</td>
                            </tr>
                            <tr>
                                <td><code>sender_info</code></td>
                                <td>object</td>
                                <td>✅</td>
                                <td>اطلاعات تفصیلی فرستنده</td>
                                <td>-</td>
                            </tr>
                            <tr style="background: #f8f9fa;">
                                <td style="padding-left: 30px;"><code>sender_info.id</code></td>
                                <td>integer</td>
                                <td>✅</td>
                                <td>شناسه فرستنده</td>
                                <td>10</td>
                            </tr>
                            <tr style="background: #f8f9fa;">
                                <td style="padding-left: 30px;"><code>sender_info.name</code></td>
                                <td>string</td>
                                <td>✅</td>
                                <td>نام فرستنده</td>
                                <td>علی احمدی</td>
                            </tr>
                            <tr style="background: #f8f9fa;">
                                <td style="padding-left: 30px;"><code>sender_info.role</code></td>
                                <td>string</td>
                                <td>✅</td>
                                <td>نقش فرستنده</td>
                                <td>Member</td>
                            </tr>
                            <tr style="background: #f8f9fa;">
                                <td style="padding-left: 30px;"><code>sender_info.type</code></td>
                                <td>string</td>
                                <td>✅</td>
                                <td>نوع فرستنده: user, manager, support</td>
                                <td>user</td>
                            </tr>
                        </tbody>
                    </table>

                    <h4 style="color: var(--success-color); margin-top: 20px;">✅ نمونه پاسخ - پیام از کاربر:</h4>
                    <div class="json-viewer">
                        <pre><code class="language-json">[
    {
        "id": 1,
        "ticket": 5,
        "user": {
            "id": 10,
            "fullname": "علی احمدی",
            "role": "Member"
        },
        "content": "من نمی‌توانم وارد حساب کاربری خود شوم",
        "message_type": "text",
        "content_image": null,
        "is_read": true,
        "created_at": "2023-01-01T10:00:00Z",
        "is_self": true,
        "deposit_name": null,
        "sender_type": "user",
        "sender_info": {
            "id": 10,
            "name": "علی احمدی",
            "role": "User",
            "type": "user"
        }
    }
]</code></pre>
                    </div>

                    <h4 style="color: var(--success-color); margin-top: 20px;">✅ نمونه پاسخ - پیام از پشتیبانی:</h4>
                    <div class="json-viewer">
                        <pre><code class="language-json">[
    {
        "id": 2,
        "ticket": 5,
        "user": {
            "id": 15,
            "fullname": "پشتیبانی فنی",
            "role": "Support"
        },
        "content": "مشکل شما بررسی شد و حل خواهد شد",
        "message_type": "text",
        "content_image": null,
        "is_read": true,
        "created_at": "2023-01-01T11:00:00Z",
        "is_self": false,
        "deposit_name": null,
        "sender_type": "support",
        "sender_info": {
            "id": 15,
            "name": "پشتیبانی فنی",
            "role": "Support",
            "type": "support"
        }
    }
]</code></pre>
                    </div>

                    <h4 style="color: var(--success-color); margin-top: 20px;">✅ نمونه پاسخ - پیام از مدیر صندوق:</h4>
                    <div class="json-viewer">
                        <pre><code class="language-json">[
    {
        "id": 3,
        "ticket": 8,
        "user": {
            "id": 20,
            "fullname": "صندوق خانوادگی",
            "role": "Owner"
        },
        "content": "مشکل شما بررسی و حل شد",
        "message_type": "text",
        "content_image": null,
        "is_read": true,
        "created_at": "2023-01-01T12:00:00Z",
        "is_self": false,
        "deposit_name": "صندوق خانوادگی",
        "sender_type": "manager",
        "sender_info": {
            "id": 20,
            "name": "صندوق خانوادگی",
            "role": "Owner",
            "type": "manager"
        }
    }
]</code></pre>
                    </div>
                </div>

                <!-- Create Message Section -->
                <div class="parameters-section">
                    <h3 class="section-title">
                        <i class="fas fa-paper-plane"></i>
                        4. ارسال پیام جدید
                    </h3>

                    <div class="endpoint-header" style="margin-bottom: 15px;">
                        <span class="endpoint-method method-post">POST</span>
                        <code class="endpoint-url">/api/ticket/{ticket_id}/messages/create/</code>
                    </div>

                    <p><strong>توضیحات:</strong> ارسال پیام جدید در گزارش. پیام‌ها می‌توانند از نوع متن یا تصویر باشند.</p>

                    <!-- Request Parameters Table -->
                    <h4 style="color: var(--secondary-color); margin-top: 20px;">📋 پارامترهای درخواست:</h4>
                    <table class="parameters-table">
                        <thead>
                            <tr>
                                <th>فیلد</th>
                                <th>نوع</th>
                                <th>الزامی</th>
                                <th>توضیحات</th>
                                <th>مثال</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>content</code></td>
                                <td>string</td>
                                <td>شرطی</td>
                                <td>متن پیام (الزامی برای پیام متنی)</td>
                                <td>لطفاً این مشکل را بررسی کنید</td>
                            </tr>
                            <tr>
                                <td><code>message_type</code></td>
                                <td>string</td>
                                <td>❌</td>
                                <td>نوع پیام: text یا image (پیش‌فرض: text)</td>
                                <td>text</td>
                            </tr>
                            <tr>
                                <td><code>content_image</code></td>
                                <td>string</td>
                                <td>شرطی</td>
                                <td>مسیر تصویر (الزامی برای پیام تصویری)</td>
                                <td>/tmp/abc123-image.jpg</td>
                            </tr>
                        </tbody>
                    </table>

                    <h4 style="color: var(--secondary-color); margin-top: 20px;">📤 نمونه درخواست (پیام متنی):</h4>
                    <div class="json-viewer">
                        <pre><code class="language-json">{
    "content": "لطفاً این مشکل را بررسی کنید",
    "message_type": "text"
}</code></pre>
                    </div>

                    <h4 style="color: var(--secondary-color); margin-top: 20px;">📤 نمونه درخواست (پیام تصویری):</h4>
                    <div class="json-viewer">
                        <pre><code class="language-json">{
    "content": "تصویر مشکل",
    "message_type": "image",
    "content_image": "/tmp/abc123-image.jpg"
}</code></pre>
                    </div>

                    <h4 style="color: var(--success-color); margin-top: 20px;">✅ نمونه پاسخ موفق - پیام از کاربر:</h4>
                    <div class="json-viewer">
                        <pre><code class="language-json">{
    "id": 1,
    "ticket": 5,
    "user": {
        "id": 10,
        "fullname": "علی احمدی",
        "role": "Member"
    },
    "content": "لطفاً این مشکل را بررسی کنید",
    "message_type": "text",
    "content_image": null,
    "is_read": false,
    "created_at": "2023-01-01T10:00:00Z",
    "is_self": true,
    "deposit_name": null,
    "sender_type": "user",
    "sender_info": {
        "id": 10,
        "name": "علی احمدی",
        "role": "Member",
        "type": "user"
    }
}</code></pre>
                    </div>

                    <h4 style="color: var(--success-color); margin-top: 20px;">✅ نمونه پاسخ موفق - پیام از مدیر:</h4>
                    <div class="json-viewer">
                        <pre><code class="language-json">{
    "id": 2,
    "ticket": 8,
    "user": {
        "id": 20,
        "fullname": "صندوق خانوادگی",
        "role": "Owner"
    },
    "content": "مشکل شما بررسی شد",
    "message_type": "text",
    "content_image": null,
    "is_read": false,
    "created_at": "2023-01-01T11:00:00Z",
    "is_self": false,
    "deposit_name": "صندوق خانوادگی",
    "sender_type": "manager",
    "sender_info": {
        "id": 20,
        "name": "صندوق خانوادگی",
        "role": "Owner",
        "type": "manager"
    }
}</code></pre>
                    </div>

                    <h4 style="color: var(--success-color); margin-top: 20px;">✅ نمونه پاسخ موفق - پیام تصویری:</h4>
                    <div class="json-viewer">
                        <pre><code class="language-json">{
    "id": 3,
    "ticket": 5,
    "user": {
        "id": 10,
        "fullname": "علی احمدی",
        "role": "Member"
    },
    "content": "تصویر مشکل",
    "message_type": "image",
    "content_image": "https://example.com/media/ticket_messages/2023/01/image.jpg",
    "is_read": false,
    "created_at": "2023-01-01T11:30:00Z",
    "is_self": true,
    "deposit_name": null,
    "sender_type": "user",
    "sender_info": {
        "id": 10,
        "name": "علی احمدی",
        "role": "Member",
        "type": "user"
    }
}</code></pre>
                    </div>
                </div>

                <!-- Important Notes Section -->
                <div class="parameters-section">
                    <h3 class="section-title">
                        <i class="fas fa-info-circle"></i>
                        نکات مهم
                    </h3>

                    <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid var(--secondary-color);">
                        <ul style="margin: 0; padding-left: 20px;">
                            <li><strong>احراز هویت:</strong> تمام API ها نیاز به توکن احراز هویت دارند</li>
                            <li><strong>نوع تیکت:</strong> برای گزارش، ticket_type باید "report" باشد</li>
                            <li><strong>موضوع فعال:</strong> subject_id باید مربوط به موضوع فعال باشد</li>
                            <li><strong>پیام اولیه:</strong> description به عنوان اولین پیام در گزارش ثبت می‌شود</li>
                            <li><strong>آپلود تصویر:</strong> ابتدا تصویر را با /api/upload-tmp/ آپلود کنید</li>
                            <li><strong>انواع فرستنده (sender_type):</strong>
                                <ul style="margin-top: 5px;">
                                    <li><code>user</code>: پیام از طرف کاربر عادی</li>
                                    <li><code>manager</code>: پیام از طرف مدیر یا مالک صندوق</li>
                                    <li><code>support</code>: پیام از طرف پشتیبانی فنی</li>
                                </ul>
                            </li>
                            <li><strong>نمایش نام:</strong>
                                <ul style="margin-top: 5px;">
                                    <li><strong>کاربران:</strong> نام واقعی کاربر نمایش داده می‌شود</li>
                                    <li><strong>مدیران:</strong> نام صندوق به همراه نقش (Owner/Admin) نمایش داده می‌شود</li>
                                    <li><strong>پشتیبانی:</strong> نام "پشتیبانی فنی" نمایش داده می‌شود</li>
                                </ul>
                            </li>
                            <li><strong>ساختار sender_info:</strong> شامل id، name، role و type فرستنده است</li>
                            <li><strong>نوع پیام:</strong> اگر message_type مشخص نشود، به صورت پیش‌فرض "text" در نظر گرفته می‌شود</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Custom Ticket Messages Documentation -->
            <div class="endpoint-section" id="ticket-messages-list">
                <div class="endpoint-header">
                    <h2 class="endpoint-title">مشاهده پیام‌های تیکت (ناظر)</h2>
                    <span class="endpoint-method method-get">GET</span>
                    <code class="endpoint-url">/api/tickets/{ticket_id}/messages/</code>
                </div>

                <p class="endpoint-description">
                    این API برای ناظران طراحی شده تا بتوانند پیام‌های تیکت را مشاهده کنند و بفهمند کی به کی پیام فرستاده است.
                    این API شامل اطلاعات کامل تیکت، شرکت‌کنندگان (ارسال‌کننده و دریافت‌کننده) و لیست پیام‌ها با pagination است.
                </p>

                <!-- Response Structure Section -->
                <div class="parameters-section">
                    <h3 class="section-title">
                        <i class="fas fa-code"></i>
                        ساختار Response
                    </h3>

                    <p><strong>توضیحات:</strong> Response شامل سه بخش اصلی است:</p>
                    <ul style="margin-bottom: 20px;">
                        <li><strong>ticket:</strong> اطلاعات کامل تیکت</li>
                        <li><strong>ticket_participants:</strong> ارسال‌کننده و دریافت‌کننده</li>
                        <li><strong>results:</strong> لیست پیام‌ها با pagination</li>
                    </ul>

                    <h4 style="color: var(--success-color); margin-top: 20px;">✅ نمونه Response کامل:</h4>
                    <div class="json-viewer" style="direction: ltr; text-align: left;">
                        <pre><code class="language-json">{
    "count": 25,
    "next": "http://example.com/api/tickets/180/messages/?page=2",
    "previous": null,
    "ticket": {
        "id": 180,
        "subject": "مشکل در پرداخت قسط",
        "ticket_type": "ticket",
        "is_closed": false,
        "created_at": "2025-07-19T10:30:00.000000",
        "closed_at": null,
        "user": {
            "id": 92,
            "fullname": "کمیل نادری"
        },
        "deposit": {
            "id": 15,
            "title": "صندوق هزینه‌های جاری مسجد",
            "deposit_type": "Poll"
        },
        "report_subject_info": null
    },
    "ticket_participants": {
        "sender": {
            "id": 92,
            "fullname": "کمیل نادری",
            "role": "Ticket Creator",
            "type": "user"
        },
        "receiver": {
            "id": 45,
            "fullname": "صندوق هزینه‌های جاری مسجد",
            "role": "Owner",
            "type": "manager"
        }
    },
    "results": [
        {
            "id": 352,
            "ticket": 180,
            "user": {
                "id": 92,
                "fullname": "کمیل نادری",
                "role": "Member"
            },
            "content": "سلام، مشکلی در پرداخت دارم",
            "message_type": "text",
            "content_image": null,
            "is_read": true,
            "created_at": "2025-07-20T03:13:48.732042",
            "is_self": true,
            "deposit_name": "صندوق هزینه‌های جاری مسجد",
            "sender_type": "user",
            "sender_info": {
                "id": 92,
                "name": "کمیل نادری",
                "role": "Member",
                "type": "user"
            },
            "sender_id": 92
        },
        {
            "id": 353,
            "ticket": 180,
            "user": {
                "id": 45,
                "fullname": "صندوق هزینه‌های جاری مسجد",
                "role": "Owner"
            },
            "content": "مشکل شما بررسی شد",
            "message_type": "text",
            "content_image": null,
            "is_read": true,
            "created_at": "2025-07-20T04:25:12.123456",
            "is_self": false,
            "deposit_name": "صندوق هزینه‌های جاری مسجد",
            "sender_type": "manager",
            "sender_info": {
                "id": 45,
                "name": "صندوق هزینه‌های جاری مسجد",
                "role": "Owner",
                "type": "manager"
            },
            "sender_id": 45
        }
    ]
}</code></pre>
                    </div>

                    <!-- Logic Explanation Section -->
                    <h3 class="section-title">
                        <i class="fas fa-cogs"></i>
                        منطق تشخیص شرکت‌کنندگان
                    </h3>

                    <p><strong>الگوریتم تشخیص:</strong> سیستم بر اساس نوع تیکت و اطلاعات مربوطه، ارسال‌کننده و دریافت‌کننده را تشخیص می‌دهد.</p>

                    <h4 style="color: var(--secondary-color); margin-top: 20px;">🎯 مرحله 1: تعیین Sender (ارسال‌کننده اولیه)</h4>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid var(--success-color); margin-bottom: 20px;">
                        <p><strong>قانون:</strong> کسی که تیکت را ایجاد کرده (<code>ticket.user</code>) همیشه <strong>sender</strong> اولیه محسوب می‌شود.</p>
                        <div class="json-viewer" style="direction: ltr; text-align: left; margin-top: 10px;">
                            <pre><code class="language-python"># سازنده تیکت همیشه sender اولیه است
ticket_creator = ticket.user
participants["sender"] = {
    "id": ticket_creator.id,
    "fullname": ticket_creator.fullname,
    "role": "Ticket Creator",
    "type": "user"
}</code></pre>
                        </div>
                    </div>

                    <h4 style="color: var(--secondary-color); margin-top: 20px;">🎯 مرحله 2: تعیین Receiver بر اساس نوع تیکت</h4>

                    <div style="background: #fff3cd; padding: 15px; border-radius: 8px; border-left: 4px solid var(--warning-color); margin-bottom: 15px;">
                        <h5 style="color: var(--warning-color); margin-bottom: 10px;">📋 حالت 1: تیکت گزارش</h5>
                        <p><strong>شرط:</strong> <code>ticket_type == 'report'</code></p>
                        <p><strong>Receiver:</strong> پشتیبانی فنی</p>
                        <div class="json-viewer" style="direction: ltr; text-align: left; margin-top: 10px;">
                            <pre><code class="language-python">if ticket.ticket_type == 'report':
    participants["receiver"] = {
        "id": 0,
        "fullname": "پشتیبانی فنی",
        "role": "Support",
        "type": "support"
    }</code></pre>
                        </div>
                    </div>

                    <div style="background: #d1ecf1; padding: 15px; border-radius: 8px; border-left: 4px solid var(--secondary-color); margin-bottom: 15px;">
                        <h5 style="color: var(--secondary-color); margin-bottom: 10px;">📋 حالت 2: تیکت مربوط به صندوق</h5>
                        <p><strong>شرط:</strong> <code>ticket.deposit</code> موجود باشد</p>
                        <p><strong>Receiver:</strong> مدیر یا مالک صندوق</p>
                        <p><strong>نکته مهم:</strong> نام صندوق نمایش داده می‌شود (نه نام مدیر)</p>
                        <div class="json-viewer" style="direction: ltr; text-align: left; margin-top: 10px;">
                            <pre><code class="language-python">elif ticket.deposit:
    # پیدا کردن مدیر یا مالک صندوق
    manager = DepositMembership.objects.filter(
        deposit=ticket.deposit,
        role__in=[DepositMembership.Role.ADMIN, DepositMembership.Role.OWNER],
        is_active=True
    ).first()

    if manager:
        participants["receiver"] = {
            "id": manager.user.id,
            "fullname": ticket.deposit.title,  # نام صندوق!
            "role": manager.role,
            "type": "manager"
        }</code></pre>
                        </div>
                    </div>

                    <div style="background: #f8d7da; padding: 15px; border-radius: 8px; border-left: 4px solid var(--danger-color); margin-bottom: 20px;">
                        <h5 style="color: var(--danger-color); margin-bottom: 10px;">📋 حالت 3: تیکت عمومی</h5>
                        <p><strong>شرط:</strong> بدون صندوق</p>
                        <p><strong>Receiver:</strong> پشتیبانی عمومی</p>
                        <div class="json-viewer" style="direction: ltr; text-align: left; margin-top: 10px;">
                            <pre><code class="language-python">else:
    participants["receiver"] = {
        "id": 0,
        "fullname": "پشتیبانی عمومی",
        "role": "Support",
        "type": "support"
    }</code></pre>
                        </div>
                    </div>

                    <!-- Different Ticket Types Section -->
                    <h3 class="section-title">
                        <i class="fas fa-tags"></i>
                        انواع مختلف تیکت و Participants
                    </h3>

                    <h4 style="color: var(--secondary-color); margin-top: 20px;">🎯 1. تیکت مربوط به صندوق:</h4>
                    <div class="json-viewer" style="direction: ltr; text-align: left;">
                        <pre><code class="language-json">"ticket_participants": {
    "sender": {
        "id": 92,
        "fullname": "کمیل نادری",
        "role": "Ticket Creator",
        "type": "user"
    },
    "receiver": {
        "id": 45,
        "fullname": "صندوق خانوادگی",  // نام صندوق
        "role": "Owner",
        "type": "manager"
    }
}</code></pre>
                    </div>

                    <h4 style="color: var(--secondary-color); margin-top: 20px;">🎯 2. تیکت گزارش:</h4>
                    <div class="json-viewer" style="direction: ltr; text-align: left;">
                        <pre><code class="language-json">"ticket_participants": {
    "sender": {
        "id": 92,
        "fullname": "کمیل نادری",
        "role": "Ticket Creator",
        "type": "user"
    },
    "receiver": {
        "id": 0,
        "fullname": "پشتیبانی فنی",
        "role": "Support",
        "type": "support"
    }
}</code></pre>
                    </div>

                    <h4 style="color: var(--secondary-color); margin-top: 20px;">🎯 3. تیکت عمومی (بدون صندوق):</h4>
                    <div class="json-viewer" style="direction: ltr; text-align: left;">
                        <pre><code class="language-json">"ticket_participants": {
    "sender": {
        "id": 92,
        "fullname": "کمیل نادری",
        "role": "Ticket Creator",
        "type": "user"
    },
    "receiver": {
        "id": 0,
        "fullname": "پشتیبانی عمومی",
        "role": "Support",
        "type": "support"
    }
}</code></pre>
                    </div>

                    <!-- Practical Example Section -->
                    <h3 class="section-title">
                        <i class="fas fa-lightbulb"></i>
                        مثال عملی از تست انجام شده
                    </h3>

                    <div style="background: #e7f3ff; padding: 20px; border-radius: 8px; border-left: 4px solid var(--secondary-color); margin-bottom: 20px;">
                        <h5 style="color: var(--secondary-color); margin-bottom: 15px;">📊 اطلاعات تیکت تست:</h5>
                        <ul style="margin-bottom: 15px;">
                            <li><strong>سازنده تیکت:</strong> Test User (ID: 112)</li>
                            <li><strong>صندوق:</strong> تست (ID: 126)</li>
                            <li><strong>مالک صندوق:</strong> محمد امین (ID: 94)</li>
                            <li><strong>نوع تیکت:</strong> ticket (مربوط به صندوق)</li>
                        </ul>

                        <h6 style="color: var(--success-color);">✅ نتیجه الگوریتم:</h6>
                        <div class="json-viewer" style="direction: ltr; text-align: left; margin-top: 10px;">
                            <pre><code class="language-json">"ticket_participants": {
    "sender": {
        "id": 112,
        "fullname": "Test User",
        "role": "Ticket Creator",
        "type": "user"
    },
    "receiver": {
        "id": 94,                    // ID محمد امین
        "fullname": "تست",          // نام صندوق (نه نام محمد امین!)
        "role": "Owner",
        "type": "manager"
    }
}</code></pre>
                        </div>
                    </div>

                    <!-- Summary Table Section -->
                    <h3 class="section-title">
                        <i class="fas fa-table"></i>
                        جدول خلاصه الگوریتم
                    </h3>

                    <table class="parameters-table" style="margin-bottom: 20px;">
                        <thead>
                            <tr>
                                <th>نوع تیکت</th>
                                <th>Sender</th>
                                <th>Receiver</th>
                                <th>نام نمایشی Receiver</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>report</code></td>
                                <td>سازنده تیکت</td>
                                <td>پشتیبانی فنی</td>
                                <td>"پشتیبانی فنی"</td>
                            </tr>
                            <tr style="background: #f8f9fa;">
                                <td><code>ticket</code> (با صندوق)</td>
                                <td>سازنده تیکت</td>
                                <td>مدیر/مالک صندوق</td>
                                <td>نام صندوق</td>
                            </tr>
                            <tr>
                                <td><code>ticket</code> (بدون صندوق)</td>
                                <td>سازنده تیکت</td>
                                <td>پشتیبانی عمومی</td>
                                <td>"پشتیبانی عمومی"</td>
                            </tr>
                        </tbody>
                    </table>

                    <!-- Message Flow Detection -->
                    <h3 class="section-title">
                        <i class="fas fa-exchange-alt"></i>
                        تشخیص جریان پیام‌ها
                    </h3>

                    <p><strong>نحوه تشخیص فرستنده هر پیام:</strong></p>
                    <div class="json-viewer" style="direction: ltr; text-align: left; margin-bottom: 20px;">
                        <pre><code class="language-javascript">// در کلاینت
const participants = response.ticket_participants;
const message = response.results[0];

if (message.sender_id === participants.sender.id) {
    // پیام از سازنده تیکت → مدیر صندوق
    console.log(`${participants.sender.fullname} → ${participants.receiver.fullname}`);
} else {
    // پیام از مدیر صندوق → سازنده تیکت
    console.log(`${participants.receiver.fullname} → ${participants.sender.fullname}`);
}</code></pre>
                    </div>

                    <div style="background: #d4edda; padding: 15px; border-radius: 8px; border-left: 4px solid var(--success-color); margin-bottom: 20px;">
                        <h6 style="color: var(--success-color); margin-bottom: 10px;">💡 مثال جریان پیام‌ها:</h6>
                        <ul style="margin: 0;">
                            <li><strong>پیام 1:</strong> Test User → تست (صندوق)</li>
                            <li><strong>پیام 2:</strong> Test User → تست (صندوق)</li>
                            <li><strong>پیام 3:</strong> تست (صندوق) → Test User</li>
                        </ul>
                    </div>

                    <!-- Pagination Section -->
                    <h3 class="section-title">
                        <i class="fas fa-list"></i>
                        Pagination و Navigation
                    </h3>

                    <p><strong>پارامترهای URL:</strong></p>
                    <ul style="margin-bottom: 20px;">
                        <li><code>page</code>: شماره صفحه (پیش‌فرض: 1)</li>
                        <li><code>page_size</code>: تعداد پیام در هر صفحه (پیش‌فرض: 20)</li>
                    </ul>

                    <h4 style="color: var(--secondary-color); margin-top: 20px;">📋 مثال درخواست:</h4>
                    <div class="json-viewer" style="direction: ltr; text-align: left;">
                        <pre><code class="language-bash">GET /api/tickets/180/messages/?page=2&page_size=10</code></pre>
                    </div>

                    <h4 style="color: var(--secondary-color); margin-top: 20px;">📋 فیلدهای Pagination:</h4>
                    <ul style="margin-bottom: 20px;">
                        <li><strong>count:</strong> تعداد کل پیام‌ها</li>
                        <li><strong>next:</strong> URL صفحه بعدی (null اگر وجود نداشته باشد)</li>
                        <li><strong>previous:</strong> URL صفحه قبلی (null اگر وجود نداشته باشد)</li>
                        <li><strong>results:</strong> آرایه پیام‌های صفحه فعلی</li>
                    </ul>

                    <!-- Usage Examples Section -->
                    <h3 class="section-title">
                        <i class="fas fa-code"></i>
                        نحوه استفاده در کلاینت
                    </h3>

                    <h4 style="color: var(--secondary-color); margin-top: 20px;">💡 JavaScript Example:</h4>
                    <div class="json-viewer" style="direction: ltr; text-align: left;">
                        <pre><code class="language-javascript">// دریافت پیام‌های تیکت
async function loadTicketMessages(ticketId, page = 1) {
    const response = await fetch(`/api/tickets/${ticketId}/messages/?page=${page}`);
    const data = await response.json();

    // اطلاعات تیکت (فقط بار اول نیاز است)
    if (page === 1) {
        displayTicketInfo(data.ticket);
        setupParticipants(data.ticket_participants);
    }

    // پیام‌های صفحه فعلی
    displayMessages(data.results);

    // کنترل pagination
    setupPagination({
        current: page,
        hasNext: !!data.next,
        hasPrevious: !!data.previous,
        total: data.count
    });
}

// تشخیص ارسال‌کننده و دریافت‌کننده هر پیام
function identifyMessageFlow(message, participants) {
    const sender = participants.sender.id === message.sender_id
        ? participants.sender
        : participants.receiver;

    const receiver = participants.sender.id === message.sender_id
        ? participants.receiver
        : participants.sender;

    console.log(`${sender.fullname} → ${receiver.fullname}`);
}</code></pre>
                    </div>

                    <!-- Important Notes Section -->
                    <h3 class="section-title">
                        <i class="fas fa-exclamation-triangle"></i>
                        نکات مهم
                    </h3>

                    <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid var(--secondary-color);">
                        <ul style="margin: 0; padding-left: 20px;">
                            <li><strong>احراز هویت:</strong> این API نیاز به توکن احراز هویت دارد</li>
                            <li><strong>دسترسی ناظر:</strong> فقط کاربران با دسترسی ناظر می‌توانند از این API استفاده کنند</li>
                            <li><strong>نمایش نام مدیران:</strong> برای مدیران صندوق، نام صندوق نمایش داده می‌شود</li>
                            <li><strong>تشخیص فرستنده:</strong> از فیلد <code>sender_id</code> برای تشخیص فرستنده هر پیام استفاده کنید</li>
                            <li><strong>Pagination:</strong> اطلاعات <code>ticket</code> و <code>ticket_participants</code> در همه صفحات یکسان است</li>
                            <li><strong>انواع تیکت:</strong>
                                <ul style="margin-top: 5px;">
                                    <li><code>ticket</code>: تیکت عادی مربوط به صندوق</li>
                                    <li><code>report</code>: گزارش کاربر</li>
                                </ul>
                            </li>
                            <li><strong>انواع شرکت‌کننده:</strong>
                                <ul style="margin-top: 5px;">
                                    <li><code>user</code>: کاربر عادی</li>
                                    <li><code>manager</code>: مدیر یا مالک صندوق</li>
                                    <li><code>support</code>: پشتیبانی فنی</li>
                                </ul>
                            </li>
                            <li><strong>خوانده شدن پیام‌ها:</strong> پیام‌های خوانده‌نشده به صورت خودکار علامت‌گذاری می‌شوند</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript for Functionality -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>

    <script>
        // Toggle sidebar for mobile
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('mobile-open');
        }

        // Toggle endpoints list
        function toggleEndpoints(endpointsId) {
            const endpointsList = document.getElementById(endpointsId);
            const icon = document.getElementById(endpointsId.replace('-endpoints', '-icon'));
            const header = icon.closest('.app-header');

            endpointsList.classList.toggle('expanded');
            icon.classList.toggle('rotated');
            header.classList.toggle('active');
        }

        // Scroll to endpoint section
        function scrollToEndpoint(endpointId) {
            const element = document.getElementById(endpointId);
            if (element) {
                element.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });

                // Highlight the endpoint temporarily
                element.style.boxShadow = '0 0 20px rgba(52, 152, 219, 0.3)';
                setTimeout(() => {
                    element.style.boxShadow = '0 4px 20px rgba(0,0,0,0.08)';
                }, 2000);
            }

            // Close sidebar on mobile after selection
            if (window.innerWidth <= 768) {
                document.getElementById('sidebar').classList.remove('mobile-open');
            }
        }

        // Show request example
        function showRequest(endpointId, requestType) {
            // Hide all request examples for this endpoint
            const allRequests = document.querySelectorAll(`[id^="${endpointId}-request-"]`);
            allRequests.forEach(request => {
                request.style.display = 'none';
            });

            // Show selected request
            const selectedRequest = document.getElementById(`${endpointId}-request-${requestType}`);
            if (selectedRequest) {
                selectedRequest.style.display = 'block';
            }

            // Update tab active state
            const requestTabs = event.target.closest('.request-tabs').querySelectorAll('.request-tab');
            requestTabs.forEach(tab => tab.classList.remove('active'));
            event.target.classList.add('active');
        }

        // Show response example
        function showResponse(endpointId, responseType) {
            // Hide all response examples for this endpoint
            const allResponses = document.querySelectorAll(`[id^="${endpointId}-response-"]`);
            allResponses.forEach(response => {
                response.style.display = 'none';
            });

            // Show selected response
            const selectedResponse = document.getElementById(`${endpointId}-response-${responseType}`);
            if (selectedResponse) {
                selectedResponse.style.display = 'block';
            }

            // Update tab active state
            const responseTabs = event.target.closest('.response-tabs').querySelectorAll('.response-tab');
            responseTabs.forEach(tab => tab.classList.remove('active'));
            event.target.classList.add('active');
        }

        // Show response schema
        function showResponseSchema(endpointId, schemaType) {
            // Hide all response schemas for this endpoint
            const allSchemas = document.querySelectorAll(`[id^="${endpointId}-schema-"]`);
            allSchemas.forEach(schema => {
                schema.style.display = 'none';
            });

            // Show selected schema
            const selectedSchema = document.getElementById(`${endpointId}-schema-${schemaType}`);
            if (selectedSchema) {
                selectedSchema.style.display = 'block';
            }

            // Update tab active state
            const schemaTabs = event.target.closest('.response-tabs').querySelectorAll('.response-tab');
            schemaTabs.forEach(tab => tab.classList.remove('active'));
            event.target.classList.add('active');
        }

        // Format JSON in response examples
        document.addEventListener('DOMContentLoaded', function() {
            const jsonViewers = document.querySelectorAll('.json-viewer pre code');
            jsonViewers.forEach(viewer => {
                try {
                    const jsonText = viewer.textContent;
                    const jsonObj = JSON.parse(jsonText);
                    const formattedJson = JSON.stringify(jsonObj, null, 2);
                    viewer.textContent = formattedJson;
                } catch (e) {
                    // If it's not valid JSON, leave it as is
                    console.log('Not valid JSON:', e);
                }
            });

            // Initialize Prism.js for syntax highlighting
            if (typeof Prism !== 'undefined') {
                Prism.highlightAll();
            }
        });

        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(event) {
            if (window.innerWidth <= 768) {
                const sidebar = document.getElementById('sidebar');
                const mobileToggle = document.querySelector('.mobile-toggle');

                if (!sidebar.contains(event.target) && !mobileToggle.contains(event.target)) {
                    sidebar.classList.remove('mobile-open');
                }
            }
        });
    </script>

    <!-- Guide API Documentation Section -->
    <div class="endpoint-section" id="guides-api">
        <h2><i class="fas fa-question-circle text-info"></i> راهنماها (Guides API)</h2>

        <div class="endpoint-card">
            <div class="endpoint-header">
                <span class="method get">GET</span>
                <span class="endpoint-url">/api/test/guides/</span>
                <span class="endpoint-title">دریافت لیست راهنماها</span>
            </div>

            <div class="endpoint-content">
                <div class="description">
                    <h4><i class="fas fa-info-circle text-primary"></i> توضیحات</h4>
                    <p>این API لیست کاملی از راهنماهای موجود برای صفحات و فیلدهای مختلف سیستم را برمی‌گرداند.</p>

                    <h5><i class="fas fa-star text-warning"></i> ویژگی‌ها</h5>
                    <ul>
                        <li>بدون نیاز به احراز هویت</li>
                        <li>بدون pagination</li>
                        <li>شامل محتوای کامل راهنماها</li>
                        <li>فقط راهنماهای فعال نمایش داده می‌شوند</li>
                    </ul>
                </div>

                <div class="request-section">
                    <h4><i class="fas fa-arrow-up text-success"></i> درخواست</h4>
                    <div class="code-block">
GET /api/test/guides/
Content-Type: application/json
                    </div>
                </div>

                <div class="response-section">
                    <h4><i class="fas fa-arrow-down text-info"></i> پاسخ موفق (200)</h4>
                    <div class="code-block">
[
    {
        "page_name": "create-poll-deposit",
        "field_name": "lottery_month_count",
        "is_active": true,
        "contents": [
            {
                "title": "تعداد ماه‌های قرعه‌کشی",
                "description": "این فیلد تعداد ماه‌هایی که صندوق قرعه‌کشی فعال خواهد بود را مشخص می‌کند.",
                "order": 0
            },
            {
                "title": "نکات مهم",
                "description": "• حداقل مدت زمان: 6 ماه\n• حداکثر مدت زمان: 60 ماه",
                "order": 1
            }
        ]
    },
    {
        "page_name": "create-poll-deposit",
        "field_name": "lottery_per_month_count",
        "is_active": true,
        "contents": [
            {
                "title": "تعداد قرعه‌کشی در ماه",
                "description": "این فیلد مشخص می‌کند که در هر ماه چند بار قرعه‌کشی انجام شود.",
                "order": 0
            }
        ]
    }
]
                    </div>
                </div>

                <div class="fields-section">
                    <h4><i class="fas fa-list text-secondary"></i> فیلدهای پاسخ</h4>
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>فیلد</th>
                                <th>نوع</th>
                                <th>توضیحات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>page_name</code></td>
                                <td>string</td>
                                <td>نام صفحه به صورت slug</td>
                            </tr>
                            <tr>
                                <td><code>field_name</code></td>
                                <td>string</td>
                                <td>نام فیلد به صورت slug (اختیاری)</td>
                            </tr>
                            <tr>
                                <td><code>is_active</code></td>
                                <td>boolean</td>
                                <td>وضعیت فعال/غیرفعال راهنما</td>
                            </tr>
                            <tr>
                                <td><code>contents</code></td>
                                <td>array</td>
                                <td>لیست محتوای راهنما</td>
                            </tr>
                            <tr>
                                <td><code>contents.title</code></td>
                                <td>string</td>
                                <td>عنوان محتوا</td>
                            </tr>
                            <tr>
                                <td><code>contents.description</code></td>
                                <td>string</td>
                                <td>توضیحات کامل</td>
                            </tr>
                            <tr>
                                <td><code>contents.order</code></td>
                                <td>integer</td>
                                <td>ترتیب نمایش محتوا</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="usage-section">
                    <h4><i class="fas fa-code text-warning"></i> نحوه استفاده</h4>
                    <div class="code-block">
// JavaScript Example
fetch('/api/test/guides/')
    .then(response => response.json())
    .then(guides => {
        guides.forEach(guide => {
            console.log(`Page: ${guide.page_name}`);
            if (guide.field_name) {
                console.log(`Field: ${guide.field_name}`);
            }
            guide.contents.forEach(content => {
                console.log(`- ${content.title}: ${content.description}`);
            });
        });
    });
                    </div>
                </div>
            </div>

            <!-- Notification System Documentation -->
            <div class="endpoint-section" id="notification-system">
                <div class="endpoint-header">
                    <h2 class="endpoint-title">سیستم نوتیفیکیشن</h2>
                    <span class="endpoint-method method-multiple">SYSTEM</span>
                    <code class="endpoint-url">Notification Service</code>
                </div>

                <p class="endpoint-description">
                    سیستم نوتیفیکیشن برای ارسال اطلاع‌رسانی‌های مختلف به کاربران طراحی شده است.
                    این سیستم از FCM (Firebase Cloud Messaging) برای ارسال push notification و
                    ذخیره‌سازی محلی در دیتابیس برای نمایش در اپلیکیشن استفاده می‌کند.
                </p>

                <!-- Region-Aware Notifications Section -->
                <div class="parameters-section">
                    <h3 class="section-title">
                        <i class="fas fa-map-marker-alt"></i>
                        نوتیفیکیشن‌های آگاه از ریجن (Region-Aware Notifications)
                    </h3>

                    <p><strong>ویژگی جدید:</strong> تمام نوتیفیکیشن‌ها حالا شامل <code>region_id</code> در فیلد <code>data</code> هستند تا بتوان آن‌ها را بر اساس ریجن فعلی کاربر فیلتر کرد.</p>

                    <div style="background: #d1ecf1; padding: 20px; border-radius: 8px; border-left: 4px solid var(--secondary-color); margin-bottom: 20px;">
                        <h4 style="color: var(--secondary-color); margin-bottom: 15px;">🎯 منطق region_id:</h4>
                        <ul style="margin: 0; padding-left: 20px;">
                            <li><strong>نوتیفیکیشن‌های مربوط به صندوق:</strong> region_id = ریجن صندوق</li>
                            <li><strong>نوتیفیکیشن‌های مربوط به تیکت:</strong> region_id = ریجن صندوق مربوطه</li>
                            <li><strong>نوتیفیکیشن‌های عمومی:</strong> region_id ممکن است موجود نباشد</li>
                        </ul>
                    </div>

                    <h4 style="color: var(--secondary-color); margin-top: 20px;">📊 ساختار data جدید:</h4>
                    <div class="json-viewer">
                        <pre><code class="language-json">{
    "title": "تایید درخواست عضویت",
    "message": "درخواست عضویت شما مورد تأیید قرار گرفت.",
    "data": {
        "model": "Deposit",
        "id": 456,
        "region_id": 123,  // اضافه شده
        "help": "انتقال به سینگل پیج صندوق"
    }
}</code></pre>
                    </div>

                    <!-- Notification Types Table -->
                    <h4 style="color: var(--secondary-color); margin-top: 20px;">📋 انواع نوتیفیکیشن و region_id:</h4>
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>نوع نوتیفیکیشن</th>
                                <th>منبع region_id</th>
                                <th>مثال</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>send_deposit_request_approved</code></td>
                                <td>deposit.region.id</td>
                                <td>تایید عضویت در صندوق</td>
                            </tr>
                            <tr>
                                <td><code>send_deposit_request_rejected</code></td>
                                <td>deposit.region.id</td>
                                <td>رد درخواست عضویت</td>
                            </tr>
                            <tr>
                                <td><code>send_loan_request_approved</code></td>
                                <td>deposit.region.id</td>
                                <td>تایید درخواست وام</td>
                            </tr>
                            <tr>
                                <td><code>send_loan_request_rejected</code></td>
                                <td>deposit.region.id</td>
                                <td>رد درخواست وام</td>
                            </tr>
                            <tr>
                                <td><code>send_new_ticket_message_notification</code></td>
                                <td>ticket.region.id</td>
                                <td>پیام جدید در تیکت</td>
                            </tr>
                            <tr>
                                <td><code>send_payment_created_notification_to_owner</code></td>
                                <td>deposit.region.id</td>
                                <td>اطلاع پرداخت به مالک</td>
                            </tr>
                            <tr>
                                <td><code>send_withdrawal_approved_notification_to_owner</code></td>
                                <td>deposit.region.id</td>
                                <td>تایید برداشت</td>
                            </tr>
                            <tr>
                                <td><code>send_lottery_announcement_to_all_members</code></td>
                                <td>deposit.region.id</td>
                                <td>اعلام قرعه‌کشی</td>
                            </tr>
                            <tr>
                                <td><code>send_lottery_winner_notification</code></td>
                                <td>deposit.region.id</td>
                                <td>اعلام برنده قرعه‌کشی</td>
                            </tr>
                            <tr>
                                <td><code>send_voting_poll_created_notification</code></td>
                                <td>deposit.region.id</td>
                                <td>ایجاد نظرسنجی</td>
                            </tr>
                            <tr>
                                <td><code>send_withdrawal_request_created_notification</code></td>
                                <td>deposit.region.id</td>
                                <td>ثبت درخواست برداشت</td>
                            </tr>
                        </tbody>
                    </table>

                    <!-- Action Keys Section -->
                    <h4 style="color: var(--success-color); margin-top: 20px;">🔑 کلیدهای Action برای کلاینت:</h4>
                    <p><strong>ویژگی جدید:</strong> تمام نوتیفیکیشن‌ها حالا شامل فیلد <code>action</code> و <code>help</code> هستند تا کلاینت بتواند عملیات مناسب را انجام دهد.</p>

                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>کلید Action</th>
                                <th>توضیحات Help</th>
                                <th>عملیات کلاینت</th>
                                <th>مثال استفاده</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>deposit_request_approved</code></td>
                                <td>انتقال به سینگل پیج صندوق</td>
                                <td>باز کردن صفحه جزئیات صندوق</td>
                                <td>Navigate to deposit details</td>
                            </tr>
                            <tr>
                                <td><code>deposit_request_rejected</code></td>
                                <td>انتقال به لیست صندوق‌ها</td>
                                <td>باز کردن لیست صندوق‌ها</td>
                                <td>Navigate to deposits list</td>
                            </tr>
                            <tr>
                                <td><code>loan_request_approved</code></td>
                                <td>انتقال به سینگل پیج صندوق</td>
                                <td>باز کردن صفحه جزئیات صندوق</td>
                                <td>Navigate to deposit details</td>
                            </tr>
                            <tr>
                                <td><code>loan_request_rejected</code></td>
                                <td>انتقال به سینگل پیج صندوق</td>
                                <td>باز کردن صفحه جزئیات صندوق</td>
                                <td>Navigate to deposit details</td>
                            </tr>
                            <tr>
                                <td><code>payment_created</code></td>
                                <td>انتقال به سینگل پیج صندوق</td>
                                <td>باز کردن صفحه جزئیات صندوق</td>
                                <td>Navigate to deposit details</td>
                            </tr>
                            <tr>
                                <td><code>withdrawal_approved</code></td>
                                <td>انتقال به سینگل پیج صندوق</td>
                                <td>باز کردن صفحه جزئیات صندوق</td>
                                <td>Navigate to deposit details</td>
                            </tr>
                            <tr>
                                <td><code>lottery_announcement</code></td>
                                <td>انتقال به سینگل پیج صندوق</td>
                                <td>باز کردن صفحه جزئیات صندوق</td>
                                <td>Navigate to deposit details</td>
                            </tr>
                            <tr>
                                <td><code>lottery_winner</code></td>
                                <td>انتقال به سینگل پیج صندوق</td>
                                <td>باز کردن صفحه جزئیات صندوق</td>
                                <td>Navigate to deposit details</td>
                            </tr>
                            <tr>
                                <td><code>voting_poll_created</code></td>
                                <td>انتقال به سینگل پیج صندوق</td>
                                <td>باز کردن صفحه جزئیات صندوق</td>
                                <td>Navigate to deposit details</td>
                            </tr>
                            <tr>
                                <td><code>new_ticket_message</code></td>
                                <td>انتقال به لیست تیکت‌ها</td>
                                <td>باز کردن لیست تیکت‌ها</td>
                                <td>Navigate to tickets list</td>
                            </tr>
                            <tr>
                                <td><code>withdrawal_request_created</code></td>
                                <td>انتقال به سینگل پیج صندوق</td>
                                <td>باز کردن صفحه جزئیات صندوق</td>
                                <td>Navigate to deposit details</td>
                            </tr>
                        </tbody>
                    </table>

                    <!-- Implementation Details -->
                    <h4 style="color: var(--secondary-color); margin-top: 20px;">🔧 جزئیات پیاده‌سازی:</h4>
                    <div class="json-viewer">
                        <pre><code class="language-python"># مثال از کد NotificationService
def send_deposit_request_approved(user_id: int, deposit_id: int) -> None:
    data = {
        "model": "Deposit",
        "id": deposit_id,
        "action": "deposit_request_approved",  # کلید جدید
        "help": "انتقال به سینگل پیج صندوق"    # راهنمای کلاینت
    }

    # اضافه کردن region_id از طریق deposit
    try:
        from apps.deposit.models import Deposit
        deposit = Deposit.objects.select_related('region').get(id=deposit_id)
        data["region_id"] = deposit.region.id
    except Exception as e:
        logger.warning(f"Could not find region for deposit {deposit_id}: {str(e)}")

    # ارسال نوتیفیکیشن
    NotificationService._send_and_save_notification(
        user_id=user_id,
        title=title,
        body=body,
        data=data
    )</code></pre>
                    </div>

                    <!-- Usage Examples -->
                    <h4 style="color: var(--secondary-color); margin-top: 20px;">💡 نحوه استفاده در کلاینت:</h4>
                    <div class="json-viewer">
                        <pre><code class="language-javascript">// فیلتر کردن نوتیفیکیشن‌ها بر اساس ریجن فعلی کاربر
function filterNotificationsByCurrentRegion(notifications, currentRegionId) {
    return notifications.filter(notification => {
        // اگر region_id موجود نباشد، نوتیفیکیشن عمومی است
        if (!notification.data.region_id) {
            return true;
        }

        // فقط نوتیفیکیشن‌های مربوط به ریجن فعلی را نمایش بده
        return notification.data.region_id === currentRegionId;
    });
}

// مدیریت کلیک روی نوتیفیکیشن بر اساس action key
function handleNotificationClick(notification) {
    const { action, help, id, model } = notification.data;

    switch (action) {
        case 'deposit_request_approved':
        case 'loan_request_approved':
        case 'payment_created':
        case 'withdrawal_approved':
        case 'lottery_announcement':
        case 'lottery_winner':
        case 'voting_poll_created':
        case 'withdrawal_request_created':
            // انتقال به سینگل پیج صندوق
            navigateToDepositDetails(id);
            break;

        case 'deposit_request_rejected':
            // انتقال به لیست صندوق‌ها
            navigateToDepositsList();
            break;

        case 'new_ticket_message':
            // انتقال به لیست تیکت‌ها
            navigateToTicketsList();
            break;

        default:
            console.log('Unknown action:', action);
            console.log('Help text:', help);
    }
}

// مثال استفاده کامل
const currentRegionId = 123;
const filteredNotifications = filterNotificationsByCurrentRegion(
    allNotifications,
    currentRegionId
);

// مدیریت کلیک روی نوتیفیکیشن
filteredNotifications.forEach(notification => {
    notification.onClick = () => handleNotificationClick(notification);
});</code></pre>
                    </div>

                    <!-- Important Notes -->
                    <div style="background: #fff3cd; padding: 20px; border-radius: 8px; border-left: 4px solid var(--warning-color); margin-top: 20px;">
                        <h4 style="color: var(--warning-color); margin-bottom: 15px;">⚠️ نکات مهم:</h4>
                        <ul style="margin: 0; padding-left: 20px;">
                            <li><strong>سازگاری با گذشته:</strong> نوتیفیکیشن‌های قدیمی ممکن است region_id نداشته باشند</li>
                            <li><strong>کلیدهای Action:</strong> تمام نوتیفیکیشن‌ها حالا شامل action و help هستند</li>
                            <li><strong>مدیریت خطا:</strong> در صورت عدم یافتن deposit، فقط warning لاگ می‌شود</li>
                            <li><strong>کارایی:</strong> استفاده از select_related('region') برای کاهش تعداد query</li>
                            <li><strong>منطق کسب‌وکار:</strong> region_id همیشه مربوط به کنتکست کسب‌وکار است (صندوق/تیکت)</li>
                            <li><strong>Navigation:</strong> کلاینت می‌تواند بر اساس action مناسب عمل کند</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

</body>
</html>
