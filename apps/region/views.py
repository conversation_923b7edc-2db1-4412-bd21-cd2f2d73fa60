from rest_framework.generics import ListAPIView, GenericAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from .models import Region, UserRegion, InvitationLink
from .serializers import RegionListSerializer, ChangeUserRegionSerializer, UserRegionSerializer, InvitationLinkSerializer, UserRegionDetailSerializer
from .docs import region_list_swagger, change_user_region_swagger, user_invitation_links_list_swagger, create_invitation_link_swagger, user_region_detail_swagger


class RegionListAPIView(ListAPIView):
    """
    API view to list regions that the user is a member of
    """
    serializer_class = RegionListSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Get only regions that the current user is a member of"""
        user = self.request.user
        user_region_ids = user.region_memberships.values_list('region_id', flat=True)
        return Region.objects.filter(id__in=user_region_ids).order_by('name')

    @region_list_swagger
    def get(self, request, *args, **kwargs):
        """Get list of user's regions with membership status"""
        return super().get(request, *args, **kwargs)


class ChangeUserRegionAPIView(GenericAPIView):
    """
    API view to change user's current active region from their existing memberships
    """
    serializer_class = ChangeUserRegionSerializer
    permission_classes = [IsAuthenticated]

    @change_user_region_swagger
    def post(self, request, *args, **kwargs):
        """Change user's current active region"""
        serializer = self.get_serializer(data=request.data)
        if not serializer.is_valid():
            return Response({
                'status': 'error',
                'message': 'Validation failed',
                'errors': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)

        region_id = serializer.validated_data['region_id']
        user = request.user

        # Check if region exists
        try:
            region = Region.objects.get(id=region_id)
        except Region.DoesNotExist:
            return Response({
                'status': 'error',
                'message': 'Region not found'
            }, status=status.HTTP_404_NOT_FOUND)

        # Check if user is a member of this region
        try:
            user_region = UserRegion.objects.get(user=user, region=region)
        except UserRegion.DoesNotExist:
            return Response({
                'status': 'error',
                'message': 'You are not a member of this region'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Use the user model method to set current region
        success = user.set_current_region(region)

        if success:
            return Response({
                'status': 'success',
                'message': 'Current region changed successfully',
            }, status=status.HTTP_200_OK)
        else:
            return Response({
                'status': 'error',
                'message': 'Failed to change current region'
            }, status=status.HTTP_400_BAD_REQUEST)


class UserInvitationLinksListAPIView(ListAPIView):
    """
    API view to list all invitation links created by the current user
    """
    serializer_class = InvitationLinkSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Get invitation links for the current user's current region"""
        user = self.request.user
        try:
            current_membership = user.current_region_membership
            if current_membership:
                return InvitationLink.objects.filter(user_region=current_membership)
            return InvitationLink.objects.none()
        except:
            return InvitationLink.objects.none()

    @user_invitation_links_list_swagger
    def get(self, request, *args, **kwargs):
        """Get list of user's invitation links with usage status"""
        return super().get(request, *args, **kwargs)


class CreateInvitationLinkAPIView(GenericAPIView):
    """
    API view to create a new invitation link for the current user
    """
    permission_classes = [IsAuthenticated]

    @create_invitation_link_swagger
    def post(self, request, *args, **kwargs):
        """Create a new invitation link for the user's region"""
        # Check if user has a current region membership
        current_membership = request.user.current_region_membership
        if not current_membership:
            return Response({
                'status': 'error',
                'message': 'User must have a current region to create invitation links.'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Create the invitation link directly
        invitation_link = InvitationLink.objects.create(
            user_region=current_membership,
            invitation_code=request.user.generate_ref_link()
        )

        # Return the created invitation link data
        response_serializer = InvitationLinkSerializer(invitation_link)
        return Response({
            'status': 'success',
            'message': 'Invitation link created successfully.',
            'invitation_link': response_serializer.data
        }, status=status.HTTP_201_CREATED)


class UserRegionDetailAPIView(GenericAPIView):
    """
    API view to get current user's region details as an object
    """
    serializer_class = UserRegionDetailSerializer
    permission_classes = [IsAuthenticated]

    @user_region_detail_swagger
    def get(self, request, *args, **kwargs):
        """Get current user's region details"""
        user = request.user
        
        # Check if user has a current region membership
        current_membership = user.current_region_membership
        if not current_membership:
            return Response({
                'status': 'error',
                'message': 'User is not a member of any region or has no current region set'
            }, status=status.HTTP_404_NOT_FOUND)

        region = current_membership.region

        # Serialize and return the user's region data
        serializer = self.get_serializer(region)
        return Response(serializer.data, status=status.HTTP_200_OK)
