import json
from django.utils.translation import gettext_lazy as _
from django.contrib import admin, messages
from django.urls import reverse, path
from django.utils.html import format_html
from django.db.models import Sum, Count, F, Q
from django.templatetags.static import static
from django.shortcuts import render, get_object_or_404, redirect
from django.http import HttpResponseRedirect

from unfold.admin import ModelAdmin, TabularInline, StackedInline
from unfold.contrib.filters.admin import RangeDateFilter, RangeNumericFilter
from unfold.contrib.filters.admin import BooleanRadioFilter, RangeDateFilter, RangeNumericFilter, AutocompleteSelectFilter
from unfold.forms import AdminPasswordChangeForm, UserChangeForm, UserCreationForm
from unfold.decorators import display, action
from unfold.sections import TableSection, TemplateSection

from .filters import MemberUserFilter

from .views import PollDepositDetailView, ReportingDepositDetailView, SavingDepositDetailView

from utils.json_editor_field import JsonEditorWidget
from utils.admin import project_admin_site
from apps.deposit.models import (
    Deposit, DepositMembership, PollDeposit, SavingDeposit, ReportingDeposit, 
    DepositDueDate, DepositMedia, DepositMediaImage
)
from utils.schema import get_rules_deposit_schema
from django import forms
from .forms import BaseDepositAdminForm, PollDepositAdminForm, SavingDepositAdminForm, ReportingDepositAdminForm


class DepositMembershipInline(StackedInline):
    model = DepositMembership
    extra = 1
    fields = ('user', 'role', 'requested_unit_count', 'monthly_installment_amount', 'is_active')
    # readonly_fields = ('joined_date',)
    autocomplete_fields = ('user',)
    tab=True
    # can_delete = True
    # show_change_link = True
    # hide_title = True  


class DepositDueDateInline(TabularInline):
    model = DepositDueDate
    extra = 0
    fields = ('due_date_number', 'due_date', 'is_completed', 'is_lottery_completed')
    can_delete = True
    show_change_link = True
    hide_title = True  # Unfold specific option to hide title row
    # tab=True

class DepositForm(forms.ModelForm):
    class Meta:
        model = Deposit
        fields = '__all__'
        widgets = {
            'rules': JsonEditorWidget(attrs={'schema': get_rules_deposit_schema(), }),
        }


class BaseDepositAdminMixin(ModelAdmin):
    form = BaseDepositAdminForm
    """Base mixin for deposit admin classes with common functionality"""
    list_display = (
        'deposit_info', 'owner_info', 'deposit_status',  'members_count', 'jalali_created'
    )
    list_filter = (
        ('is_active', BooleanRadioFilter),
        ('created', RangeDateFilter),
        'region',
        ('members__user', MemberUserFilter),
    )
    ordering = ('-created',)
    readonly_fields = ('created', 'updated')
    search_fields = (
        'title', 'description', 'owner__fullname', 'owner__phone_number'
    )
    compressed_fields = False    
    list_filter_submit = True  # Enable submit button for filters
    autocomplete_fields = ('owner', 'region')
    inlines = [DepositMembershipInline, DepositDueDateInline]
    
    fieldsets = (
        (None, {"fields": (("deposit_type"),)}),
        (
            _("Basic Information"),
            {
                "fields": ("title", "description", "owner", "region", "is_active"),
                "classes": ["tab"],
            },
        ),
        (
            _('Financial Details'), {
                'fields': ('total_debt_amount', 'unit_amount', 'payment_cycle'),
                "classes": ["tab"],
            }
        ),
        (
            _('Membership Settings'), {
                'fields': ('max_unit_per_request', 'max_members_count'),
                "classes": ["tab"],
            }
        ),
        (
            _('Dates'), {
                'fields': ('start_date', 'initial_lottery_date', 'validity_duration', 'lottery_month_count'),
                "classes": ["tab"],
            }
        ),
        (
            _('Rules'), {
                'fields': ('rules',),
                "classes": ["tab"],
            }
        ),
        (
            _('System Information'), {
                'fields': ('created', 'updated'),
                "classes": ["tab"],
            }
        ),
    )
    def has_delete_permission(self, request, obj=None):
        return True
        
    def get_deleted_objects(self, objs, request):
        return [], 0, set(), []
        
    def delete_model(self, request, obj):
        obj.soft_delete()
        messages.success(request, _("Deposit has been soft deleted successfully."))
    
    def delete_queryset(self, request, queryset):
        for obj in queryset:
            obj.soft_delete()
        messages.success(request, _("Selected deposits have been soft deleted successfully."))
        
    def delete_view(self, request, object_id, extra_context=None):
        obj = self.get_object(request, object_id)
        if obj is None:
            return self._get_obj_does_not_exist_redirect(request, self.model._meta, object_id)
            
        if request.method == 'POST':
            obj.soft_delete()
            messages.success(request, _("Deposit has been soft deleted successfully."))
            return redirect(reverse(f'admin:{self.model._meta.app_label}_{self.model._meta.model_name}_changelist'))
            
        context = {
            'title': _('Are you sure?'),
            'object_name': str(obj),
            'object': obj,
            'opts': self.model._meta,
            'app_label': self.model._meta.app_label,
            **self.admin_site.each_context(request),
            **(extra_context or {}),
        }
        return render(request, 'admin/delete_confirmation.html', context)
        
    def changelist_view(self, request, extra_context=None):
        if request.method == 'POST' and 'action' in request.POST and request.POST['action'] == 'delete_selected':
            selected = request.POST.getlist('_selected_action')
            if selected:
                queryset = self.get_queryset(request).filter(pk__in=selected)
                for obj in queryset:
                    obj.soft_delete()
                messages.success(request, _("Selected deposits have been soft deleted successfully."))
                return redirect(reverse(f'admin:{self.model._meta.app_label}_{self.model._meta.model_name}_changelist'))
                
        return super().changelist_view(request, extra_context)
        
    
    def members_count(self, obj):
        return obj.members.count()
    members_count.short_description = _('Members Count')
    
    def get_queryset(self, request):
        qs = super().get_queryset(request)
        qs = qs.prefetch_related('members', 'due_dates')
        qs.filter(is_active=True)
        return qs
    
    @display(description='Deposit Name', header=True)
    def deposit_info(self, obj):
        # Get the appropriate SVG icon based on deposit type
        if obj.deposit_type == Deposit.DepositType.POLL:
            deposit_path = static("images/deposit-poll.svg") or None
        elif obj.deposit_type == Deposit.DepositType.SAVING:
            deposit_path = static("images/deposit-saving.svg") or None
        elif obj.deposit_type == Deposit.DepositType.REPORTING:
            deposit_path = static("images/deposit-reporting.svg") or None
        else:
            deposit_path = static("images/deposit-default.svg") or None

        # Get the display name of the deposit type
        deposit_type_display = obj.get_deposit_type_display()
        
        # For PollDeposit, make the title a link to the detail view
        # title = obj.title
        # if obj.__class__.__name__ == 'PollDeposit':
        #     url = reverse(
        #         'admin:deposit_polldeposit_detail',
        #         args=[obj.pk],
        #         current_app=self.admin_site.name,
        #     )
        #     title = format_html('<a href="{}">{}</a>', url, obj.title)

        return [
            obj.title,
            None,
            # deposit_type_display,
            None,
            {
                "path": deposit_path,
                "height": 33,
                "width": 38,
                "borderless": True,
                "squared": True,
            },  
        ]

    @display(description='Status', label={
        True: 'success',   # Active deposits in green
        False: 'danger',   # Inactive deposits in red
    })
    def deposit_status(self, obj):
        return obj.is_active, _('Active') if obj.is_active else _('Inactive')
        
    @display(description='Owner', header=True)
    def owner_info(self, obj):
        # Get the owner's avatar URL or use a default image
        avatar_path = obj.owner.avatar.url if obj.owner and obj.owner.avatar else static("images/user1.svg")
        
        return [
            obj.owner.fullname if obj.owner else _('No Owner'),
            # obj.owner.phone_number,
            None,  # No subtitle
            # obj.owner.get_initials() if obj.owner else '',  # Initials as fallback
            # {
            #     "path": avatar_path,
            #     "height": 33,
            #     "width": 38,
            #     "borderless": True,
            #     "squared": True,
            # },  
        ]
        
    @display(description=_('Created Date'))
    def jalali_created(self, obj):
        from utils.date_utils import format_jalali_date
        if obj.created:
            return format_jalali_date(obj.created, "%Y/%m/%d %H:%M")
        return "-"


class DepositAdmin(BaseDepositAdminMixin):
    """Admin for all deposits"""
    pass


class DepositMembersTableSection(TableSection):
    verbose_name = _("Deposit Members")
    related_name = "members"
    height = 380
    fields = [
        "user_fullname",
        "shares_and_installment",
        "role",
    ]

    @admin.display(description=_("User Full Name"))
    def user_fullname(self, instance):
        return instance.user.fullname
        
    @admin.display(description=_("Shares & Monthly Installment"))
    def shares_and_installment(self, instance):
        shares = instance.requested_unit_count if instance.requested_unit_count is not None else 0
        installment = instance.monthly_installment_amount if instance.monthly_installment_amount is not None else 0
        
        formatted_installment = f"{installment:,}" if installment else "0"
        return f"{shares} {_('shares')} - {formatted_installment}"


# class DepositChartSection(TemplateSection):
    # template_name = "deposit/deposit_poll_section.html"





class PollDepositAdmin(BaseDepositAdminMixin):
    """Admin for poll deposits"""
    form = PollDepositAdminForm
    fieldsets = (
        (None, {"fields": ()}),
        (
            _("Basic Information"),
            {
                "fields": ("title", "description", "owner", "region", "is_active"),
                "classes": ["tab"],
            },
        ),
        (
            _('Financial Details'), {
                'fields': ('total_debt_amount', 'unit_amount', 'payment_cycle'),
                "classes": ["tab"],
            }
        ),
        (
            _('Membership Settings'), {
                'fields': ('max_unit_per_request', 'max_members_count'),
                "classes": ["tab"],
            }
        ),
        (
            _('Poll Specific'), {
                'fields': ('initial_lottery_date', 'lottery_month_count', 'lottery_per_month_count'),
                "classes": ["tab"],
            }
        ),
        (
            _('Rules'), {
                'fields': ('rules',),
                "classes": ["tab"],
            }
        ),
        (
            _('System Information'), {
                'fields': ('created', 'updated'),
                "classes": ["tab"],
            }
        ),
    )
    
    
    def get_urls(self):
        """Add custom URLs for the admin."""
        urls = super().get_urls()
        custom_urls = [
            # Custom detail view using UnfoldModelAdminViewMixin
            path(
                '<path:object_id>/detail/',
                self.admin_site.admin_view(
                    PollDepositDetailView.as_view(model_admin=self)
                ),
                name='deposit_polldeposit_detail',
            ),
            # Original change form view - this will be accessible from the detail view
            path(
                '<path:object_id>/edit/',
                self.admin_site.admin_view(self.original_change_view),
                name='deposit_polldeposit_original_change',
            ),
        ]
        return custom_urls + urls
    
    def original_change_view(self, request, object_id, form_url='', extra_context=None):
        """Original change view for PollDeposit."""
        # Call the parent's change_view directly to bypass our override
        return super(BaseDepositAdminMixin, self).change_view(
            request, object_id, form_url, extra_context
        )
        
    def change_view(self, request, object_id, form_url='', extra_context=None):
        """Override change view to redirect to custom detail view."""
        # Redirect to our custom detail view
        url = reverse(
            'admin:deposit_polldeposit_detail',
            args=[object_id],
            current_app=self.admin_site.name,
        )
        return HttpResponseRedirect(url)


    
    def get_queryset(self, request):
        """Filter deposits to only show poll deposits"""
        queryset = super().get_queryset(request)
        return queryset.filter(deposit_type=Deposit.DepositType.POLL, is_active=True).prefetch_related(
            'members',
            'members__user'
        )
    
    def save_model(self, request, obj, form, change):
        """Ensure deposit_type is set to Poll"""
        obj.deposit_type = Deposit.DepositType.POLL
        super().save_model(request, obj, form, change)


class ReportingDepositAdmin(BaseDepositAdminMixin):
    """Admin for reporting deposits"""
    form = ReportingDepositAdminForm
    
    fieldsets = (
        (
            _("Basic Information"),
            {
                "fields": ("title", "description", "owner", "region", "is_active"),
                "classes": ["tab"],
            },
        ),
        (
            _('Financial Details'), {
                'fields': ('unit_amount', 'payment_cycle', 'validity_duration'),
                "classes": ["tab"],
            }
        ),
        (
            _('Membership Settings'), {
                'fields': ('max_unit_per_request', 'max_members_count'),
                "classes": ["tab"],
            }
        ),
        (
            _('Rules'), {
                'fields': ('rules',),
                "classes": ["tab"],
            }
        ),
        (
            _('System Information'), {
                'fields': ('created', 'updated'),
                "classes": ["tab"],
            }
        ),
    )
    
    
    def get_urls(self):
        """Add custom URLs for the admin."""
        urls = super().get_urls()
        custom_urls = [
            # Custom detail view using UnfoldModelAdminViewMixin
            path(
                '<path:object_id>/detail/',
                self.admin_site.admin_view(
                    ReportingDepositDetailView.as_view(model_admin=self)
                ),
                name='deposit_reportingdeposit_detail',
            ),
            # Original change form view - this will be accessible from the detail view
            path(
                '<path:object_id>/edit/',
                self.admin_site.admin_view(self.original_change_view),
                name='deposit_reportingdeposit_original_change',
            ),
        ]
        return custom_urls + urls
    
    def original_change_view(self, request, object_id, form_url='', extra_context=None):
        """Original change view for PollDeposit."""
        # Call the parent's change_view directly to bypass our override
        return super(BaseDepositAdminMixin, self).change_view(
            request, object_id, form_url, extra_context
        )
        
    def change_view(self, request, object_id, form_url='', extra_context=None):
        """Override change view to redirect to custom detail view."""
        # Redirect to our custom detail view
        url = reverse(
            'admin:deposit_reportingdeposit_detail',
            args=[object_id],
            current_app=self.admin_site.name,
        )
        return HttpResponseRedirect(url)


    
    def get_queryset(self, request):
        """Filter deposits to only show poll deposits"""
        queryset = super().get_queryset(request)
        return queryset.filter(deposit_type=Deposit.DepositType.REPORTING, is_active=True).prefetch_related(
            'members',
            'members__user'
        )
    
    def save_model(self, request, obj, form, change):
        """Ensure deposit_type is set to Poll"""
        obj.deposit_type = Deposit.DepositType.REPORTING
        super().save_model(request, obj, form, change)

class SavingDepositAdmin(BaseDepositAdminMixin):
    """Admin for saving deposits"""
    form = SavingDepositAdminForm
    
    fieldsets = (
        (
            _("Basic Information"),
            {
                "fields": ("title", "description", "owner", "region", "is_active"),
                "classes": ["tab"],
            },
        ),
        (
            _('Financial Details'), {
                'fields': ('unit_amount', 'payment_cycle', 'validity_duration', 'start_date'),
                "classes": ["tab"],
            }
        ),
        (
            _('Membership Settings'), {
                'fields': ('max_unit_per_request', 'max_members_count'),
                "classes": ["tab"],
            }
        ),
        (
            _('Rules'), {
                'fields': ('rules',),
                "classes": ["tab"],
            }
        ),
        (
            _('System Information'), {
                'fields': ('created', 'updated'),
                "classes": ["tab"],
            }
        ),
    )
    
    
    def get_urls(self):
        """Add custom URLs for the admin."""
        urls = super().get_urls()
        custom_urls = [
            # Custom detail view using UnfoldModelAdminViewMixin
            path(
                '<path:object_id>/detail/',
                self.admin_site.admin_view(
                    SavingDepositDetailView.as_view(model_admin=self)
                ),
                name='deposit_savingdeposit_detail',
            ),
            # Original change form view - this will be accessible from the detail view
            path(
                '<path:object_id>/edit/',
                self.admin_site.admin_view(self.original_change_view),
                name='deposit_savingdeposit_original_change',
            ),
        ]
        return custom_urls + urls
    
    def original_change_view(self, request, object_id, form_url='', extra_context=None):
        """Original change view for SavingDeposit."""
        # Call the parent's change_view directly to bypass our override
        return super(BaseDepositAdminMixin, self).change_view(
            request, object_id, form_url, extra_context
        )
        
    def change_view(self, request, object_id, form_url='', extra_context=None):
        """Override change view to redirect to custom detail view."""
        # Redirect to our custom detail view
        url = reverse(
            'admin:deposit_savingdeposit_detail',
            args=[object_id],
            current_app=self.admin_site.name,
        )
        return HttpResponseRedirect(url)


    
    def get_queryset(self, request):
        """Filter deposits to only show saving deposits"""
        queryset = super().get_queryset(request)
        return queryset.filter(deposit_type=Deposit.DepositType.SAVING, is_active=True).prefetch_related(
            'members',
            'members__user'
        )
    
    def save_model(self, request, obj, form, change):
        """Ensure deposit_type is set to SAVING"""
        obj.deposit_type = Deposit.DepositType.SAVING
        super().save_model(request, obj, form, change)

# Register only PollDeposit model with the default admin site
# admin.site.register(PollDeposit, PollDepositAdmin)

# Register only PollDeposit model with the project_admin_site
# project_admin_site.register(Deposit, DepositAdmin)  # Commented out as requested
project_admin_site.register(PollDeposit, PollDepositAdmin)
project_admin_site.register(ReportingDeposit, ReportingDepositAdmin)
project_admin_site.register(SavingDeposit, SavingDepositAdmin)

