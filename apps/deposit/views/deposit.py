from rest_framework.generics import ListAPI<PERSON>iew, CreateAPIView, RetrieveAPIView
from django.db.models import Q, Count, Sum, Case, When, Value, IntegerField
from django.db import models
from rest_framework.exceptions import ValidationError
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework import status
from rest_framework.filters import SearchFilter
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from datetime import datetime, timedelta
from django.shortcuts import get_object_or_404
import jdatetime

from apps.deposit.doc import (
    deposit_list_swagger, deposit_detail_swagger, deposit_transaction_list_swagger,
    deposit_member_detail_swagger
)
from utils.date_utils import gregorian_to_jalali, jalali_to_gregorian
from apps.deposit.serializers import DepositSerializer, DepositMembershipSerializer, SetRoleSerializer, DepositTransactionSerializer
from apps.deposit.models import Deposit, DepositMembership, DepositDueDate
from apps.transaction.models import Transaction
from utils.exceptions import AppAPIException

from apps.deposit.pagination import NoPagination


class DepositListAPIView(ListAPIView):
    queryset = Deposit.objects.filter(is_active=True)
    serializer_class = DepositSerializer
    permission_classes = [IsAuthenticated]
    pagination_class = NoPagination  # Use the custom pagination class
    filter_backends = [SearchFilter]
    search_fields = ['title']  # Enable search on title field

    
    @deposit_list_swagger
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)
    
    
    def _filter_by_type(self, queryset):
        """Filter deposits by type (Poll/Saving/Reporting)"""
        deposit_type = self.request.query_params.get('type')
        if not deposit_type:
            return queryset
            
        valid_types = [t[0] for t in Deposit.DepositType.choices]
        if deposit_type not in valid_types:
            raise ValidationError({"type": "Invalid deposit type"})
            
        return queryset.filter(deposit_type=deposit_type)
    
    def _parse_amount_filter(self):
        """Parse amount filter parameter and validate it"""
        amount_param = self.request.query_params.get('amount')
        if not amount_param:
            return None, None, False
            
        try:
            # Extract operator and value (e.g., "10+" → 10 and '+')
            operator = amount_param[-1]
            amount_value = int(amount_param[:-1])

            # Validate the operator
            if operator not in ['+', '-', '=']:
                raise ValueError
                
            # Convert Tomans to Rials (1 Toman = 10 Rials)
            amount_in_rials = amount_value * 10
            
            return operator, amount_in_rials, True
            
        except (ValueError, IndexError):
            raise ValidationError({
                "amount": "Invalid format. Use format like '10+' (greater than 10), '5-' (less than 5), or '10=' (from 0 to 10)"
            })
    
    def _filter_by_members_count(self, queryset):
        """Filter deposits by active members count"""
        members_param = self.request.query_params.get('members')
        if not members_param:
            return queryset
            
        try:
            # Extract operator and value (e.g., "+10" → '+' and 10)
            operator = members_param[0]
            members_value = int(members_param[1:])

            # Annotate with active members count
            queryset = queryset.annotate(
                active_members_count=Count(
                    'members',
                    filter=Q(members__is_active=True)
                )
            )

            if operator == '+':
                return queryset.filter(active_members_count__gt=members_value)
            elif operator == '-':
                return queryset.filter(active_members_count__lt=members_value)
            else:
                raise ValueError
                
        except (ValueError, IndexError):
            raise ValidationError({"members": "Invalid format. Use format like '+10' or '-5'"})
    
    def _filter_by_user_membership(self, queryset):
        """Filter deposits where current user is a member"""
        is_member_param = self.request.query_params.get('is_member')
        if is_member_param and is_member_param.lower() == 'true':
            return queryset.filter(
                members__user=self.request.user,
                members__is_active=True
            )
        return queryset
    
    def _filter_and_sort_by_balance(self, queryset, operator, value):
        """
        Filter deposits by their actual balance and sort them by balance
        
        Args:
            queryset: The queryset to filter
            operator: The comparison operator ('+', '-', or '=')
            value: The balance value to compare against (in Rials)
            
        Returns:
            A queryset filtered and sorted by balance
        """
        from apps.transaction.models import Transaction
        
        # Get all deposits that passed the previous filters
        deposits = list(queryset)
        filtered_deposits = []
        deposit_balances = {}  # Store balances for sorting later
        
        for deposit in deposits:
            # Calculate the balance for each deposit
            balance = Transaction.get_total_balance(deposit)
            deposit_balances[deposit.id] = balance
            
            # Apply the balance filter
            if operator == '+' and balance > value:
                filtered_deposits.append(deposit)
            elif operator == '-' and balance < value:
                filtered_deposits.append(deposit)
            elif operator == '=' and balance <= value:
                filtered_deposits.append(deposit)
        
        # Sort deposits by balance (descending)
        if filtered_deposits:
            filtered_deposits.sort(key=lambda d: deposit_balances[d.id], reverse=True)
            
            # Convert the sorted list back to a queryset
            deposit_ids = [deposit.id for deposit in filtered_deposits]
            preserved_order = models.Case(*[models.When(pk=pk, then=pos) for pos, pk in enumerate(deposit_ids)])
            return Deposit.objects.filter(id__in=deposit_ids).order_by(preserved_order)
        
        # Return an empty queryset if no deposits match the balance filter
        return Deposit.objects.none()
    
    def _filter_by_search(self, queryset):
        """Filter deposits by search term in title"""
        search_term = self.request.query_params.get('search')
        if search_term:
            return queryset.filter(title__icontains=search_term)
        return queryset

    def get_queryset(self):
        """Get filtered and sorted deposits based on query parameters"""
        # Base queryset (only active deposits)
        queryset = Deposit.objects.filter(is_active=True)

        # Apply filters one by one
        queryset = self._filter_by_search(queryset)
        queryset = self._filter_by_type(queryset)

        # Parse amount filter parameters
        balance_filter_operator, balance_filter_value, filter_by_balance = self._parse_amount_filter()

        queryset = self._filter_by_members_count(queryset)
        queryset = self._filter_by_user_membership(queryset)

        # Apply balance filtering if needed
        if filter_by_balance:
            return self._filter_and_sort_by_balance(
                queryset,
                balance_filter_operator,
                balance_filter_value
            )

        # If no balance filtering, use the default ordering
        return queryset.order_by('-created')
    


class DepositDetailAPIView(RetrieveAPIView):
    queryset = Deposit.objects.filter(is_active=True)
    serializer_class = DepositSerializer
    permission_classes = [IsAuthenticated]
    lookup_field = 'pk'

    @deposit_detail_swagger
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)
    
 
 
 
class SetMemberRoleView(CreateAPIView):
    serializer_class = SetRoleSerializer
    
    def post(self, request, deposit_id, member_id):
        try:
            deposit = Deposit.objects.get(id=deposit_id)
        except Deposit.DoesNotExist:
            raise AppAPIException({"message": "Deposit not found."}, statuc_code=status.HTTP_404_NOT_FOUND)

        try:
            member = DepositMembership.objects.get(id=member_id, deposit=deposit)
        except DepositMembership.DoesNotExist:
            raise AppAPIException({"message": "Member not found or does not belong to this deposit."}, status_code=status.HTTP_404_NOT_FOUND)

        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)        

        new_role = serializer.validated_data['role']
        member.role = new_role
        member.save()

        return Response({"message": f"Role updated successfully."}, status=status.HTTP_200_OK)
    
    
class DepositMembersListView(ListAPIView):
    serializer_class = DepositMembershipSerializer

    def get_queryset(self):
        deposit_id = self.kwargs['deposit_id']
        try:
            deposit = Deposit.objects.get(id=deposit_id)
        except Deposit.DoesNotExist:
            return DepositMembership.objects.none()  

        # Order by role: first Owner, then Admin, then Member
        return DepositMembership.objects.filter(deposit=deposit).order_by(
            Case(
                When(role=DepositMembership.Role.OWNER, then=Value(1)),
                When(role=DepositMembership.Role.ADMIN, then=Value(2)),
                When(role=DepositMembership.Role.MEMBER, then=Value(3)),
                default=Value(4),
                output_field=IntegerField(),
            )
        )
    

    
class DepositTransactionListView(ListAPIView):
    serializer_class = DepositTransactionSerializer
    
    @deposit_transaction_list_swagger
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)
    
    def get_object(self):
        deposit_id = self.kwargs['deposit_id']
        try:
            deposit = Deposit.objects.get(id=deposit_id)
        except Deposit.DoesNotExist:
            raise AppAPIException({"message": "Deposit not found."}, status_code=status.HTTP_404_NOT_FOUND)
        return deposit
    
    def get_queryset(self):
        deposit = self.get_object()
        queryset = deposit.transactions.filter(status=Transaction.TransactionStatus.SUCCESS)

        if filter_value := self.request.query_params.get('filter'):
            # Get current Jalali date
            now_jalali = jdatetime.datetime.now()
            
            if filter_value == 'yesterday':
                # Calculate yesterday in Jalali calendar
                yesterday_jalali = now_jalali - timedelta(days=1)
                # Convert Jalali date to Gregorian for database query
                yesterday_gregorian = yesterday_jalali.togregorian()
                # Filter by exact date
                queryset = queryset.filter(created_at__date=yesterday_gregorian.date())
                
            elif filter_value == 'last_week':
                # Calculate last week in Jalali calendar
                last_week_jalali = now_jalali - timedelta(days=7)
                # Convert to Gregorian for database query
                last_week_gregorian = last_week_jalali.togregorian()
                now_gregorian = now_jalali.togregorian()
                # Filter by date range
                queryset = queryset.filter(created_at__gte=last_week_gregorian, created_at__lt=now_gregorian)
                
            elif filter_value == 'last_month':
                # Calculate last month in Jalali calendar
                if now_jalali.month == 1:
                    # If current month is Farvardin, go to previous year's Esfand
                    last_month_jalali = jdatetime.datetime(now_jalali.year - 1, 12, now_jalali.day)
                else:
                    # Otherwise, just go to previous month
                    last_month_jalali = jdatetime.datetime(now_jalali.year, now_jalali.month - 1, 
                                                          min(now_jalali.day, jdatetime.j_days_in_month[now_jalali.month - 1]))
                
                # Convert to Gregorian for database query
                last_month_gregorian = last_month_jalali.togregorian()
                now_gregorian = now_jalali.togregorian()
                # Filter by date range
                queryset = queryset.filter(created_at__gte=last_month_gregorian, created_at__lt=now_gregorian)
                
            elif filter_value == 'last_three_months':
                # Calculate three months ago in Jalali calendar
                year = now_jalali.year
                month = now_jalali.month - 3
                
                # Handle year boundary
                if month <= 0:
                    year -= 1
                    month += 12
                
                # Ensure valid day (some months have fewer days)
                day = min(now_jalali.day, jdatetime.j_days_in_month[month - 1])
                
                # Create the Jalali datetime object for three months ago
                three_months_ago_jalali = jdatetime.datetime(year, month, day)
                
                # Convert to Gregorian for database query
                three_months_ago_gregorian = three_months_ago_jalali.togregorian()
                now_gregorian = now_jalali.togregorian()
                
                # Filter by date range
                queryset = queryset.filter(created_at__gte=three_months_ago_gregorian, created_at__lt=now_gregorian)
                
        # Return the filtered queryset
        return queryset
            
class DepositTransactionMemberView(ListAPIView):
    serializer_class = DepositTransactionSerializer

    def get_object(self):
        deposit_id = self.kwargs['deposit_id']
        member_id = self.kwargs['member_id']
        try:
            deposit = Deposit.objects.get(id=deposit_id)
            member = DepositMembership.objects.get(id=member_id, deposit=deposit)
        except (Deposit.DoesNotExist, DepositMembership.DoesNotExist):
            raise AppAPIException({"message": "Deposit or member not found."}, status_code=status.HTTP_404_NOT_FOUND)
        return member.get_user

    def get_queryset(self):
        user = self.get_object()
        return user.deposit_transactions.filter(status=Transaction.TransactionStatus.SUCCESS)


class DepositMemberDetailView(APIView):
    permission_classes = [IsAuthenticated]

    @deposit_member_detail_swagger
    def get(self, request, deposit_id, member_id):
        # Get the deposit and membership objects
        deposit = get_object_or_404(Deposit, id=deposit_id)
        membership = get_object_or_404(DepositMembership, id=member_id, deposit=deposit)

        # Get the user from the membership
        user = membership.user

        # Get all due dates for this deposit that are not completed
        due_dates = DepositDueDate.objects.filter(
            deposit=deposit,
            is_completed=False
        )

        # Count unpaid installments (due dates where the user has no successful transaction)
        unpaid_installments_count = 0
        total_debt_amount = 0

        for due_date in due_dates:
            # Check if the user has a successful transaction for this due date
            has_transaction = Transaction.objects.filter(
                deposit=deposit,
                user=user,
                due_date=due_date,
                status=Transaction.TransactionStatus.SUCCESS
            ).exists()

            # If no transaction exists, increment the unpaid count and add to debt amount
            if not has_transaction:
                unpaid_installments_count += 1
                # Calculate the debt amount based on the user's monthly installment amount
                if membership.monthly_installment_amount:
                    total_debt_amount += float(membership.monthly_installment_amount)

        # Prepare the response data
        response_data = {
            'fullname': user.fullname,  # Assuming the User model has a fullname field
            'phone_number': str(user.phone_number),  # Assuming the User model has a phone field
            'shba_number': user.shba_number,
            'unpaid_installments_count': unpaid_installments_count,
            'unpaid_installments_amount': total_debt_amount,
        }

        return Response(response_data)




