from django.utils.translation import gettext_lazy as _
from django.contrib import admin
from django.templatetags.static import static
from django.db.models import Q, Sum
from django.urls import reverse
from django.utils.html import format_html

from unfold.admin import ModelAdmin
from unfold.contrib.filters.admin import (
    RangeNumericFilter, 
    RangeDateFilter, 
    BooleanRadioFilter,
    ChoicesRadioFilter
)
from unfold.decorators import display, action

from apps.payment.models import Payment
from utils.admin import project_admin_site


class HasLoanInstallmentFilter(admin.SimpleListFilter):
    """Filter for payments with loan installments"""
    title = _('Loan Installment')
    parameter_name = 'has_loan'

    def lookups(self, request, model_admin):
        return [
            ('yes', _('Includes Loan Installment')),
            ('no', _('Without Loan Installment')),
        ]

    def queryset(self, request, queryset):
        if self.value() == 'yes':
            return queryset.filter(includes_loan_installment=True)
        if self.value() == 'no':
            return queryset.filter(includes_loan_installment=False)
        return queryset


class PaymentAdmin(ModelAdmin):
    """Admin for Payment model"""
    list_display = (
        'payment_info', 'payment_type',  '_payment_status','_transaction_type', '_transaction_status', 'payment_amount', 'user_info', 'jalali_payment_date'
    )
    list_filter = (
        HasLoanInstallmentFilter,
        ('payment_date', RangeDateFilter),
        ('amount', RangeNumericFilter),
        ('total_amount', RangeNumericFilter),
        'deposit__title',
    )
    ordering = ('-payment_date',)
    readonly_fields = ('created_at', 'updated_at')
    search_fields = (
        'user__fullname', 'user__phone_number', 'deposit__title',
        'reference_number', 'gateway_name', 'authority'
    )
    compressed_fields = False    
    list_filter_submit = True  # Enable submit button for filters
    autocomplete_fields = ('user', 'transaction')
    
    fieldsets = (
        (None, {"fields": (("gateway_name", "reference_number", "status"),)}),
        (
            _("Relations"),
            {
                "fields": ("user", "deposit", "deposit_membership", "transaction"),
                "classes": ["tab"],
            },
        ),
        (
            _('Payment Details'), {
                'fields': ('amount', 'includes_loan_installment', 'loan_installment_amount', 'total_amount', 'payment_date'),
                "classes": ["tab"],
            }
        ),
        (
            _('Gateway Information'), {
                'fields': ('authority', 'payment_url', 'redirected_at'),
                "classes": ["tab"],
            }
        ),
        (
            _('System Information'), {
                'fields': ('created_at', 'updated_at'),
                "classes": ["tab"],
            }
        ),
    )
    
    def has_delete_permission(self, request, obj=None):
        return False
    
    def get_queryset(self, request):
        """Optimize queries by prefetching related objects"""
        return super().get_queryset(request).select_related(
            'user', 'deposit', 'deposit_membership', 'transaction'
        )
    
    @display(description='Payment', header=True)
    def payment_info(self, obj):        
        subtitle = ""
        if obj.includes_loan_installment:
            loan_amount = f"{obj.loan_installment_amount:,}" if obj.loan_installment_amount else "0"
            subtitle = _("Includes Loan Installment: {}").format(loan_amount)
        
        gateway_info = f"{obj.gateway_name}" if obj.gateway_name else _("No Gateway")
        if obj.reference_number:
            gateway_info += f" - {obj.reference_number}"
        
        return [
            gateway_info,
            subtitle,
            None,
        ]
    

    
    @display(description='Transaction Status')
    def _transaction_status(self, obj):
        return obj.transaction.get_status_display() if obj.transaction else _("No Transaction")

    @display(description='Transaction Type')
    def _transaction_type(self, obj):
        return obj.transaction.get_transaction_type_display() if obj.transaction else _("No Transaction")

    @display(description='Payment Status')
    def _payment_status(self, obj):
        return obj.get_status_display() 
    
    @display(description='Payment Type', label={
        True: 'success',   # Payments with loan installment in green
        False: 'info',     # Payments without loan installment in blue
    })
    def payment_type(self, obj):
        if obj.includes_loan_installment:
            return True, _('With Loan Installment')
        return False, _('Regular Payment')
    
    @display(description='Amount')
    def payment_amount(self, obj):
        formatted_amount = f"{obj.amount:,}" if obj.amount else "0"
        formatted_total = f"{obj.total_amount:,}" if obj.total_amount else "0"
        
        if obj.includes_loan_installment and obj.amount != obj.total_amount:
            return f"{formatted_amount} / {formatted_total} {_('Toman')}"
        return f"{formatted_total} {_('Toman')}"
    
    @display(description='User', header=True)
    def user_info(self, obj):        
        deposit_name = obj.deposit.title if obj.deposit else _('No Deposit')
        
        return [
            obj.user.fullname if obj.user else _('No User'),
            deposit_name,
            None,
        ]
    
    @display(description=_('Payment Date'))
    def jalali_payment_date(self, obj):
        from utils.date_utils import format_jalali_date
        if obj.payment_date:
            return format_jalali_date(obj.payment_date, "%Y/%m/%d %H:%M")
        return "-"