from rest_framework import status
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework.generics import CreateAPI<PERSON>iew
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
import logging


from apps.deposit.models import Deposit
from apps.request.models import RequestCreateDeposit
from apps.request.serializers import CreatePollDepositSerializer, CreateSavingDepositSerializer, CreateReportingDepositSerializer
from utils.exceptions import AppAPIException
from apps.request.doc import (
    create_poll_deposit_swagger, create_saving_deposit_swagger,
    create_reporting_deposit_swagger
)
from apps.region.models.region import UserRegion

logger = logging.getLogger(__name__)


class DepositCreateMixin:
    permission_classes = [IsAuthenticated]

    def create(self, request, *args, **kwargs):
        try:
            user_region = request.user.region_membership
        except UserRegion.DoesNotExist:
            raise AppAPIException(
                {"message": "You do not have permission to make this request because your membership link is invalid."}, status_code=status.HTTP_400_BAD_REQUEST
            )
        deposit_data = request.data.copy()
        deposit_serializer = self.get_serializer(data=deposit_data)
        deposit_serializer.is_valid(raise_exception=True)        
        deposit = deposit_serializer.save(
            owner=request.user,
            region=request.user.region_membership.region,
            is_active=False,
            deposit_type=self.deposit_type
        )
        
        request_deposit = RequestCreateDeposit.objects.create(
            deposit=deposit,
            user=request.user,
            status=RequestCreateDeposit.StatusChoices.PENDING
        )
        
        return Response(deposit_serializer.data, status=status.HTTP_201_CREATED)

class CreatePollDepositView(DepositCreateMixin, CreateAPIView):
    serializer_class = CreatePollDepositSerializer
    deposit_type = Deposit.DepositType.POLL

    @create_poll_deposit_swagger
    def post(self, request, *args, **kwargs):
        print(f'--->CreatePollDepositView: {request.data}')
        return super().post(request, *args, **kwargs)     

class CreateSavingDepositView(DepositCreateMixin, CreateAPIView):
    serializer_class = CreateSavingDepositSerializer
    deposit_type = Deposit.DepositType.SAVING
    
    @create_saving_deposit_swagger
    def post(self, request, *args, **kwargs):
        print(f'--->CreateSavingDepositView: {request.data}')
        return super().post(request, *args, **kwargs)     


class CreateReportingDepositView(DepositCreateMixin, CreateAPIView):
    serializer_class = CreateReportingDepositSerializer
    deposit_type = Deposit.DepositType.REPORTING
    
    @create_reporting_deposit_swagger
    def post(self, request, *args, **kwargs):
        print(f'--->CreateReportingDepositView: {request.data}')
        return super().post(request, *args, **kwargs)     
