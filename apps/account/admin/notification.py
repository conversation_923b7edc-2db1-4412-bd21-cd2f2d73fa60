from django.contrib import admin
from django.utils.translation import gettext as _
import json
from django.utils.html import format_html

from apps.account.models.notification import Notification
from unfold.admin import ModelAdmin
from utils.admin import project_admin_site


class NotificationAdmin(ModelAdmin):
    list_display = ('title', 'user', 'is_read', 'has_data', 'created_at', 'updated_at')
    list_filter = ('is_read', 'created_at', 'updated_at')
    search_fields = ('title', 'message', 'user__fullname')
    ordering = ('-created_at',)
    date_hierarchy = 'created_at'
    readonly_fields = ('created_at', 'updated_at', 'formatted_data')
    
    fieldsets = (
        (_('Notification Information'), {
            'fields': ('title', 'message', 'user', 'is_read'),
            'classes': ['tab']
        }),
        (_('Notification Data'), {
            'fields': ('data', 'formatted_data'),
            'classes': ['tab']
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ['tab']
        }),
    )
    
    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        queryset = queryset.select_related('user')
        return queryset
    
    def formatted_data(self, obj):
        """
        Format the JSON data for better readability in the admin interface
        """
        if not obj.data:
            return "-"
        
        try:
            # Pretty format the JSON data
            formatted_json = json.dumps(obj.data, indent=4, ensure_ascii=False)
            # Replace spaces with non-breaking spaces and newlines with <br> tags
            formatted_html = formatted_json.replace(' ', '&nbsp;').replace('\n', '<br>')
            return format_html('<pre style="font-family: monospace;">{}</pre>', formatted_html)
        except Exception as e:
            return f"Error formatting data: {str(e)}"
    
    formatted_data.short_description = _("Formatted Data")
            
    def has_data(self, obj):
        """
        Check if the notification has data
        """
        if obj.data:
            return format_html('<span style="color: green;">✓</span>')
        return format_html('<span style="color: red;">✗</span>')
    
    has_data.short_description = _("Has Data")


project_admin_site.register(Notification, NotificationAdmin)
